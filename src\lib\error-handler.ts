
import { toast } from 'sonner';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export class PromptStudioError extends Error {
  public readonly context: ErrorContext;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';
  public readonly recoverable: boolean;

  constructor(
    message: string,
    context: ErrorContext = {},
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    recoverable: boolean = true
  ) {
    super(message);
    this.name = 'PromptStudioError';
    this.context = {
      ...context,
      timestamp: new Date()
    };
    this.severity = severity;
    this.recoverable = recoverable;
  }
}

class ErrorHandlerService {
  private errorLog: PromptStudioError[] = [];
  private maxLogSize = 100;

  handleError(error: Error | PromptStudioError, context?: ErrorContext): void {
    const promptStudioError = error instanceof PromptStudioError 
      ? error 
      : new PromptStudioError(error.message, context, 'medium', true);

    // Add to error log
    this.errorLog.unshift(promptStudioError);
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Log to console with context
    console.error('🚨 Prompt Studio Error:', {
      message: promptStudioError.message,
      severity: promptStudioError.severity,
      context: promptStudioError.context,
      stack: promptStudioError.stack
    });

    // Show user-friendly notification
    this.showUserNotification(promptStudioError);

    // Handle critical errors
    if (promptStudioError.severity === 'critical') {
      this.handleCriticalError(promptStudioError);
    }
  }

  private showUserNotification(error: PromptStudioError): void {
    const userMessage = this.getUserFriendlyMessage(error);
    
    switch (error.severity) {
      case 'critical':
        toast.error(userMessage, {
          duration: 10000,
          action: {
            label: 'Reload',
            onClick: () => window.location.reload()
          }
        });
        break;
      case 'high':
        toast.error(userMessage, { duration: 5000 });
        break;
      case 'medium':
        toast.warning(userMessage, { duration: 3000 });
        break;
      case 'low':
        toast.info(userMessage, { duration: 2000 });
        break;
    }
  }

  private getUserFriendlyMessage(error: PromptStudioError): string {
    const action = error.context.action;
    
    if (error.message.includes('database') || error.message.includes('IndexedDB')) {
      return 'Data storage issue detected. Your work is being saved to backup storage.';
    }
    
    if (error.message.includes('AI') || error.message.includes('model')) {
      return `AI service error${action ? ` during ${action}` : ''}. Please try again.`;
    }
    
    if (error.message.includes('network') || error.message.includes('fetch')) {
      return 'Network connection issue. Please check your connection and try again.';
    }
    
    return error.recoverable 
      ? `Operation failed${action ? ` during ${action}` : ''}. Please try again.`
      : 'A critical error occurred. Please refresh the page.';
  }

  private handleCriticalError(error: PromptStudioError): void {
    // Save error details to localStorage for debugging
    try {
      const errorDetails = {
        message: error.message,
        context: error.context,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      localStorage.setItem('promptstudio_critical_error', JSON.stringify(errorDetails));
    } catch (e) {
      console.error('Failed to save critical error details:', e);
    }
  }

  getErrorLog(): PromptStudioError[] {
    return [...this.errorLog];
  }

  clearErrorLog(): void {
    this.errorLog = [];
  }

  // Wrapper functions for common error scenarios
  handleDatabaseError(error: Error, operation: string): void {
    this.handleError(
      new PromptStudioError(
        `Database operation failed: ${error.message}`,
        { action: operation, component: 'database' },
        'medium',
        true
      )
    );
  }

  handleAIServiceError(error: Error, model?: string): void {
    this.handleError(
      new PromptStudioError(
        `AI service error: ${error.message}`,
        { action: 'ai_request', component: 'ai-service', metadata: { model } },
        'high',
        true
      )
    );
  }

  handleChainExecutionError(error: Error, nodeId?: string): void {
    this.handleError(
      new PromptStudioError(
        `Chain execution failed: ${error.message}`,
        { action: 'chain_execution', component: 'chain-linker', metadata: { nodeId } },
        'high',
        true
      )
    );
  }
}

export const errorHandler = new ErrorHandlerService();

// Global error handler
window.addEventListener('error', (event) => {
  errorHandler.handleError(
    new PromptStudioError(
      event.error?.message || 'Unknown error',
      { component: 'global', action: 'runtime_error' },
      'high',
      true
    )
  );
});

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handleError(
    new PromptStudioError(
      event.reason?.message || 'Unhandled promise rejection',
      { component: 'global', action: 'promise_rejection' },
      'medium',
      true
    )
  );
});
