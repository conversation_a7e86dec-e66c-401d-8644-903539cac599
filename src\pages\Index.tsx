
import React, { useState, useEffect } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { PromptTester } from '@/components/PromptTester';
import { PromptVariations } from '@/components/PromptVariations';
import { ResultsDashboard } from '@/components/ResultsDashboard';
import { PromptHistory } from '@/components/PromptHistory';
import { Settings } from '@/components/Settings';
import { AgentSimulator } from '@/components/AgentSimulator';
import { ProjectSimulator } from '@/components/ProjectSimulator';
import { ChainLinkerCanvas } from '@/components/ChainLinkerCanvas';
import { CommunityVaultView } from '@/components/CommunityVaultView';
import { OnboardingTour } from '@/components/OnboardingTour';
import { usePromptStore } from '@/hooks/usePromptStore';
import { databaseService } from '@/lib/database-fix';
import { errorHandler } from '@/lib/error-handler';
import { toast } from 'sonner';

const Index = () => {
  const [activeView, setActiveView] = useState('tester');
  const [onboardingOpen, setOnboardingOpen] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const { results, addResult } = usePromptStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 Initializing Prompt Studio...');
        
        // Initialize database service
        await databaseService.initialize();
        
        // Check for any critical errors from previous sessions
        const criticalError = localStorage.getItem('promptstudio_critical_error');
        if (criticalError) {
          try {
            const errorData = JSON.parse(criticalError);
            console.warn('⚠️ Previous critical error detected:', errorData);
            toast.warning('Previous session ended unexpectedly. Your data has been preserved.');
            localStorage.removeItem('promptstudio_critical_error');
          } catch (e) {
            console.error('Failed to parse critical error data:', e);
          }
        }
        
        console.log('✅ Prompt Studio initialized successfully');
        toast.success('Welcome to Prompt Studio!', { duration: 2000 });
        
      } catch (error) {
        console.error('❌ Failed to initialize Prompt Studio:', error);
        errorHandler.handleError(
          error as Error,
          { component: 'app-initialization', action: 'startup' }
        );
      } finally {
        setIsInitializing(false);
      }
    };

    initializeApp();
  }, []);

  const handleResults = (newResults: any) => {
    try {
      console.log('📊 New results received:', newResults);
      // Handle results from components that generate them
    } catch (error) {
      errorHandler.handleError(
        error as Error,
        { component: 'index', action: 'handle_results' }
      );
    }
  };

  const handleRunVariations = (variations: any) => {
    try {
      console.log('🔄 Running variations:', variations);
      // Handle variation testing
    } catch (error) {
      errorHandler.handleError(
        error as Error,
        { component: 'index', action: 'handle_variations' }
      );
    }
  };

  const handleRunSimulation = (simulation: any) => {
    try {
      console.log('🤖 Running simulation:', simulation);
      // Handle agent simulation
    } catch (error) {
      errorHandler.handleError(
        error as Error,
        { component: 'index', action: 'handle_simulation' }
      );
    }
  };

  const handleRunMultiAgentSimulation = (simulation: any) => {
    try {
      console.log('👥 Running multi-agent simulation:', simulation);
      // Handle multi-agent simulation
    } catch (error) {
      errorHandler.handleError(
        error as Error,
        { component: 'index', action: 'handle_multi_agent_simulation' }
      );
    }
  };

  const renderActiveView = () => {
    if (isInitializing) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-slate-300 font-sans">Initializing Prompt Studio...</p>
          </div>
        </div>
      );
    }

    try {
      switch (activeView) {
        case 'tester':
          return <PromptTester onResults={handleResults} />;
        case 'variations':
          return <PromptVariations onRunVariations={handleRunVariations} />;
        case 'agents':
          return <AgentSimulator 
            onRunSimulation={handleRunSimulation}
            onRunMultiAgentSimulation={handleRunMultiAgentSimulation}
          />;
        case 'projects':
          return <ProjectSimulator />;
        case 'chain-linker':
          return <ChainLinkerCanvas />;
        case 'results':
          return <ResultsDashboard results={results} />;
        case 'history':
          return <PromptHistory />;
        case 'vault':
          return <CommunityVaultView />;
        case 'settings':
          return <Settings />;
        default:
          return <PromptTester onResults={handleResults} />;
      }
    } catch (error) {
      errorHandler.handleError(
        error as Error,
        { component: 'index', action: 'render_view', metadata: { activeView } }
      );
      
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-red-400 font-sans mb-4">Error loading {activeView} view</p>
            <button 
              onClick={() => setActiveView('tester')} 
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Return to Prompt Tester
            </button>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 font-sans">
      <div className="flex h-screen">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        <main className="flex-1 overflow-auto">
          <div className="h-full">
            {renderActiveView()}
          </div>
        </main>
      </div>
      
      <OnboardingTour 
        isOpen={onboardingOpen} 
        onClose={() => setOnboardingOpen(false)} 
      />
    </div>
  );
};

export default Index;
