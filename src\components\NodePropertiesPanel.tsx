import { ChainNode } from '@/lib/chain-linker';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription, DialogTrigger } from '@/components/ui/dialog';
import { X, Save } from 'lucide-react';
import { useState } from 'react';
import { usePromptStore } from '@/hooks/usePromptStore';
import { toast } from '@/hooks/use-toast';

interface NodePropertiesPanelProps {
  selectedNode: ChainNode | null;
  onUpdateNodeData: (nodeId: string, data: Partial<ChainNode['data']>) => void;
  onClose: () => void;
}

export const NodePropertiesPanel = ({ selectedNode, onUpdateNodeData, onClose }: NodePropertiesPanelProps) => {
  const { addNodeTemplate, publishItemToVault } = usePromptStore();
  const [isTemplateDialogVisible, setIsTemplateDialogVisible] = useState(false);
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateTags, setTemplateTags] = useState('');
  const [publishTemplateToVault, setPublishTemplateToVault] = useState(false);

  if (!selectedNode) return null;

  const handleSaveAsTemplate = async () => {
    if (!selectedNode || !templateName.trim()) {
      toast({
        title: "Error",
        description: "Template name is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      const savedTemplate = await addNodeTemplate({
        name: templateName,
        description: templateDescription,
        tags: templateTags.split(',').map(t => t.trim()).filter(t => t),
        nodeType: selectedNode.type,
        nodeData: JSON.parse(JSON.stringify(selectedNode.data)),
      });
      
      toast({
        title: "Template Saved",
        description: `Node template "${templateName}" has been saved.`,
      });
      
      setIsTemplateDialogVisible(false);
      setTemplateName('');
      setTemplateDescription('');
      setTemplateTags('');

      if (publishTemplateToVault && savedTemplate) {
        try {
          await publishItemToVault({
            id: savedTemplate.id,
            type: 'node_template',
            name: savedTemplate.name,
            description: savedTemplate.description,
            author: "Current User (Placeholder)",
            tags: savedTemplate.tags,
            previewSnippet: `A ${savedTemplate.nodeType} node template.`,
          });
          toast({ title: "Published to Vault", description: `Template "${savedTemplate.name}" (mock) published.`});
        } catch (vaultError) {
          console.error("Failed to publish node template to vault:", vaultError);
          toast({ title: "Vault Publish Failed", description: vaultError instanceof Error ? vaultError.message : "Unknown error", variant: "destructive"});
        }
      }
      setPublishTemplateToVault(false);

    } catch (error) {
      toast({
        title: "Error Saving Template",
        description: error instanceof Error ? error.message : "Could not save node template.",
        variant: "destructive",
      });
    }
  };

  const handleConfigChange = (configKey: string, value: any) => {
    onUpdateNodeData(selectedNode.id, {
      config: {
        ...selectedNode.data.config,
        [configKey]: value,
      },
    });
  };

  return (
    <div className="w-80 border-l border-slate-700 p-4 space-y-4 bg-slate-800/50">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-slate-200">Node Properties</h3>
        <Button onClick={onClose} size="sm" variant="ghost" className="text-slate-400 hover:text-slate-200">
          <X className="w-5 h-5" />
        </Button>
      </div>

      <Dialog open={isTemplateDialogVisible} onOpenChange={setIsTemplateDialogVisible}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="w-full border-blue-500 text-blue-300 hover:bg-blue-500/20 hover:text-blue-200">
            <Save className="w-4 h-4 mr-2" />
            Save as Template
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px] bg-slate-800 border-slate-700">
          <DialogHeader>
            <DialogTitle className="text-slate-100">Save Node as Template</DialogTitle>
            <DialogDescription className="text-slate-400">
              Save the current configuration of "{selectedNode.data.title}" as a reusable template.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="template-name" className="text-slate-300">Template Name *</Label>
              <Input
                id="template-name"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="mt-1 bg-slate-700 border-slate-600 text-white"
                placeholder="e.g., My Summarizer Node"
              />
            </div>
            <div>
              <Label htmlFor="template-description" className="text-slate-300">Description</Label>
              <Textarea
                id="template-description"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                className="mt-1 bg-slate-700 border-slate-600 text-white"
                placeholder="Describe what this template does"
              />
            </div>
            <div>
              <Label htmlFor="template-tags" className="text-slate-300">Tags (comma-separated)</Label>
              <Input
                id="template-tags"
                value={templateTags}
                onChange={(e) => setTemplateTags(e.target.value)}
                className="mt-1 bg-slate-700 border-slate-600 text-white"
                placeholder="e.g., summarization, gpt4, utility"
              />
            </div>
            <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-slate-700/50">
              <Checkbox
                id="publishTemplateToVaultCheckbox"
                checked={publishTemplateToVault}
                onCheckedChange={(checked) => setPublishTemplateToVault(Boolean(checked))}
                className="border-slate-500"
              />
              <Label htmlFor="publishTemplateToVaultCheckbox" className="text-sm text-slate-300 cursor-pointer">
                Publish this template to Community Vault (local mock)
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="ghost" onClick={() => { setIsTemplateDialogVisible(false); setPublishTemplateToVault(false);}} className="text-slate-300">Cancel</Button>
            <Button type="submit" onClick={handleSaveAsTemplate} className="bg-blue-600 hover:bg-blue-700 text-white">Save Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="space-y-4 max-h-[calc(100vh-160px)] overflow-y-auto pr-1">
        <div>
          <Label htmlFor="node-title" className="text-slate-300 text-sm">Title</Label>
          <Input
            id="node-title"
            value={selectedNode.data.title}
            onChange={(e) => onUpdateNodeData(selectedNode.id, { title: e.target.value })}
            className="bg-slate-900/50 border-slate-600 text-white mt-1"
          />
        </div>

        <div>
          <Label htmlFor="node-description" className="text-slate-300 text-sm">Description</Label>
          <Textarea
            id="node-description"
            value={selectedNode.data.description || ''}
            onChange={(e) => onUpdateNodeData(selectedNode.id, { description: e.target.value })}
            className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[60px]"
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor="node-behavior-tags" className="text-slate-300 text-sm">Behavior Tags (comma-separated)</Label>
          <Input
            id="node-behavior-tags"
            value={(selectedNode.data.behaviorTags || []).join(', ')}
            onChange={(e) => onUpdateNodeData(selectedNode.id, {
              behaviorTags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag)
            })}
            className="bg-slate-900/50 border-slate-600 text-white mt-1"
            placeholder="e.g., llm_call, data_transform"
          />
        </div>

        <div>
          <Label htmlFor="node-logic-hints" className="text-slate-300 text-sm">Logic Hints</Label>
          <Textarea
            id="node-logic-hints"
            value={selectedNode.data.logicHints || ''}
            onChange={(e) => onUpdateNodeData(selectedNode.id, { logicHints: e.target.value })}
            className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[80px]"
            placeholder="Describe this node's specific role or logic in the flow..."
            rows={3}
          />
        </div>

        {(selectedNode.type === 'prompt' || selectedNode.type === 'condition') && (
          <div>
            <Label htmlFor="node-content" className="text-slate-300 text-sm">
              {selectedNode.type === 'prompt' ? 'Prompt Content' : 'Condition Logic'}
            </Label>
            <Textarea
              id="node-content"
              value={selectedNode.data.content || ''}
              onChange={(e) => onUpdateNodeData(selectedNode.id, { content: e.target.value })}
              className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[100px]"
              placeholder={selectedNode.type === 'prompt' ? 'Enter your prompt here... {{variable}}' : 'e.g., input.value > 10'}
              rows={selectedNode.type === 'prompt' ? 5 : 3}
            />
          </div>
        )}

        {selectedNode.data.config && Object.keys(selectedNode.data.config).length > 0 && (
          <div className="space-y-3 pt-2 border-t border-slate-700/50">
            <h4 className="text-sm font-medium text-slate-300">Configuration</h4>
            {Object.entries(selectedNode.data.config).map(([key, value]) => (
              <div key={key}>
                <Label htmlFor={`config-${key}`} className="text-slate-400 text-xs capitalize">{key.replace(/([A-Z])/g, ' $1')}</Label>
                {key === 'inputSchema' || key === 'outputSchema' ? (
                  <Textarea
                    id={`config-${key}`}
                    value={String(value)}
                    onChange={(e) => handleConfigChange(key, e.target.value)}
                    className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[80px] font-mono text-xs"
                    placeholder={`Enter JSON Schema for ${key === 'inputSchema' ? 'input' : 'output'} data (optional)`}
                    rows={4}
                  />
                ) : typeof value === 'number' ? (
                  <Input
                    id={`config-${key}`}
                    type="number"
                    value={value}
                    onChange={(e) => handleConfigChange(key, parseFloat(e.target.value) || 0)}
                    className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  />
                ) : typeof value === 'boolean' ? (
                  <div className="mt-1 flex items-center">
                     <Input
                        id={`config-${key}`}
                        type="checkbox"
                        checked={value}
                        onChange={(e) => handleConfigChange(key, e.target.checked)}
                        className="h-4 w-4 rounded border-slate-600 text-blue-500 focus:ring-blue-400 bg-slate-900/50"
                     />
                     <Label htmlFor={`config-${key}`} className="ml-2 text-sm text-slate-300">
                        {value ? "Enabled" : "Disabled"}
                     </Label>
                  </div>
                ) : (
                  <Input
                    id={`config-${key}`}
                    type="text"
                    value={String(value)}
                    onChange={(e) => handleConfigChange(key, e.target.value)}
                    className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  />
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
