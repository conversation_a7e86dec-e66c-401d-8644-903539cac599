import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { PromptVariant, Agent, NodeTemplate, VaultItemSummary } from '@/store/promptStore'; // Added VaultItemSummary
import type { ChainFlow } from './chain-linker';

export interface DatabaseSchema extends DBSchema {
  prompts: {
    key: string;
    value: PromptVariant;
  };
  agents: {
    key: string;
    value: Agent;
  };
  results: {
    key: string;
    value: any;
  };
  settings: {
    key: string;
    value: {
      id: string;
      key: string;
      value: any;
      updatedAt: Date;
    };
  };
  backups: {
    key: string;
    value: {
      id: string;
      name: string;
      data: any;
      createdAt: Date;
      size: number;
    };
  };
  nodeTemplates: {
    key: string;
    value: NodeTemplate;
  };
  chainFlows: {
    key: string;
    value: ChainFlow;
  };
  communityVaultItems: {
    key: string;
    value: VaultItemSummary;
  };
  chains: {
    key: string;
    value: {
      id: string;
      name: string;
      nodes: any[];
      connections: any[];
      metadata: any;
      createdAt: Date;
    };
  };
  promptPacks: {
    key: string;
    value: {
      id: string;
      title: string;
      description: string;
      variants: any[];
      metadata: any;
      tags: string[];
      createdAt: Date;
    };
  };
}

class DatabaseService {
  private db: IDBPDatabase<DatabaseSchema> | null = null;
  private initPromise: Promise<void> | null = null;
  private readonly dbName = 'PromptStudioDB';
  private readonly version = 2;

  async initialize(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.doInitialize();
    return this.initPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      console.log(`🔄 Opening database ${this.dbName} version ${this.version}`);

      this.db = await openDB<DatabaseSchema>(this.dbName, this.version, {
        upgrade: (db, oldVersion, newVersion, transaction) => {
          console.log(`🔄 Upgrading database from version ${oldVersion} to ${newVersion}`);
          try {
            this.createStores(db);
            console.log('✅ Database stores created successfully');
          } catch (storeError) {
            console.error('❌ Failed to create database stores:', storeError);
            throw storeError;
          }
        },
        blocked() {
          console.warn('⚠️ Database upgrade blocked - another tab may be using an older version');
        },
        blocking() {
          console.warn('⚠️ Database blocking - this tab is blocking an upgrade');
        },
        terminated() {
          console.error('❌ Database connection terminated unexpectedly');
        }
      });

      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      console.error('Error details:', {
        name: (error as Error).name,
        message: (error as Error).message,
        stack: (error as Error).stack
      });

      // Try to clear the database and retry once
      if (!this.db) {
        console.log('🔄 Attempting to clear database and retry...');
        try {
          // Delete the database and try again
          await this.clearDatabase();
          this.db = await openDB<DatabaseSchema>(this.dbName, this.version, {
            upgrade: (db, oldVersion, newVersion, transaction) => {
              console.log(`🔄 Creating fresh database version ${newVersion}`);
              this.createStores(db);
            }
          });
          console.log('✅ Database recreated successfully');
        } catch (retryError) {
          console.error('❌ Database retry failed:', retryError);
          // Fallback to localStorage for critical data
          this.db = null;
        }
      }
    }
  }

  private async clearDatabase(): Promise<void> {
    try {
      const deleteRequest = indexedDB.deleteDatabase(this.dbName);
      return new Promise((resolve, reject) => {
        deleteRequest.onsuccess = () => {
          console.log('✅ Database cleared successfully');
          resolve();
        };
        deleteRequest.onerror = () => {
          console.error('❌ Failed to clear database');
          reject(new Error('Failed to clear database'));
        };
        deleteRequest.onblocked = () => {
          console.warn('⚠️ Database clear blocked');
          // Continue anyway
          resolve();
        };
      });
    } catch (error) {
      console.error('❌ Error clearing database:', error);
      throw error;
    }
  }

  private createStores(db: any): void {
    console.log('🏗️ Creating database stores...');

    try {
      // Prompts store
      if (!db.objectStoreNames.contains('prompts')) {
        console.log('Creating prompts store...');
        const promptStore = db.createObjectStore('prompts', { keyPath: 'id' });
        promptStore.createIndex('name', 'name', { unique: false });
        promptStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
      }

      // Agents store
      if (!db.objectStoreNames.contains('agents')) {
        console.log('Creating agents store...');
        const agentStore = db.createObjectStore('agents', { keyPath: 'id' });
        agentStore.createIndex('name', 'name', { unique: false });
        agentStore.createIndex('role', 'role', { unique: false });
      }

      // Results store
      if (!db.objectStoreNames.contains('results')) {
        console.log('Creating results store...');
        const resultStore = db.createObjectStore('results', { keyPath: 'id', autoIncrement: true });
        resultStore.createIndex('timestamp', 'timestamp', { unique: false });
        resultStore.createIndex('type', 'type', { unique: false });
      }

      // Settings store
      if (!db.objectStoreNames.contains('settings')) {
        console.log('Creating settings store...');
        const settingsStore = db.createObjectStore('settings', { keyPath: 'id' });
        settingsStore.createIndex('key', 'key', { unique: true });
      }

      // Backups store
      if (!db.objectStoreNames.contains('backups')) {
        console.log('Creating backups store...');
        const backupStore = db.createObjectStore('backups', { keyPath: 'id' });
        backupStore.createIndex('createdAt', 'createdAt', { unique: false });
      }

      // NodeTemplates store
      if (!db.objectStoreNames.contains('nodeTemplates')) {
        console.log('Creating nodeTemplates store...');
        const templateStore = db.createObjectStore('nodeTemplates', { keyPath: 'id' });
        templateStore.createIndex('name', 'name', { unique: false });
        templateStore.createIndex('nodeType', 'nodeType', { unique: false });
        templateStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
      }

      // ChainFlows store
      if (!db.objectStoreNames.contains('chainFlows')) {
        console.log('Creating chainFlows store...');
        const flowStore = db.createObjectStore('chainFlows', { keyPath: 'id' });
        flowStore.createIndex('name', 'name', { unique: false });
        // flowStore.createIndex('tags', 'metadata.tags', { unique: false, multiEntry: true });
      }

      // CommunityVaultItems store
      if (!db.objectStoreNames.contains('communityVaultItems')) {
        console.log('Creating communityVaultItems store...');
        const vaultStore = db.createObjectStore('communityVaultItems', { keyPath: 'vaultId' }); // Use vaultId as keyPath
        vaultStore.createIndex('type', 'type', { unique: false });
        vaultStore.createIndex('name', 'name', { unique: false });
        vaultStore.createIndex('tags', 'tags', { unique: false, multiEntry: true });
        vaultStore.createIndex('createdAt', 'createdAt', { unique: false });
      }

      // Chains store (from database-fix.ts)
      if (!db.objectStoreNames.contains('chains')) {
        console.log('Creating chains store...');
        db.createObjectStore('chains', { keyPath: 'id' });
      }

      // PromptPacks store (from database-fix.ts)
      if (!db.objectStoreNames.contains('promptPacks')) {
        console.log('Creating promptPacks store...');
        db.createObjectStore('promptPacks', { keyPath: 'id' });
      }

      console.log('✅ All database stores created successfully');
    } catch (error) {
      console.error('❌ Error creating database stores:', error);
      throw error;
    }
  }

  async add<T extends keyof DatabaseSchema>(
    storeName: T,
    data: DatabaseSchema[T]['value']
  ): Promise<void> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${(data as any).id}`;
      localStorage.setItem(storageKey, JSON.stringify(data));
      return;
    }

    try {
      await this.db.add(storeName, data);
    } catch (error) {
      console.error(`Failed to add to ${storeName}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${(data as any).id}`;
      localStorage.setItem(storageKey, JSON.stringify(data));
    }
  }

  async update<T extends keyof DatabaseSchema>(
    storeName: T,
    data: DatabaseSchema[T]['value']
  ): Promise<void> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${(data as any).id}`;
      localStorage.setItem(storageKey, JSON.stringify(data));
      return;
    }

    try {
      await this.db.put(storeName, data);
    } catch (error) {
      console.error(`Failed to update ${storeName}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${(data as any).id}`;
      localStorage.setItem(storageKey, JSON.stringify(data));
    }
  }

  async get<T extends keyof DatabaseSchema>(
    storeName: T,
    id: string
  ): Promise<DatabaseSchema[T]['value'] | undefined> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${id}`;
      const data = localStorage.getItem(storageKey);
      return data ? JSON.parse(data) : undefined;
    }

    try {
      return await this.db.get(storeName, id) || undefined;
    } catch (error) {
      console.error(`Failed to get from ${storeName}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${id}`;
      const data = localStorage.getItem(storageKey);
      return data ? JSON.parse(data) : undefined;
    }
  }

  async getAll<T extends keyof DatabaseSchema>(
    storeName: T
  ): Promise<DatabaseSchema[T]['value'][]> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      const items: DatabaseSchema[T]['value'][] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${storeName}_`)) {
          const data = localStorage.getItem(key);
          if (data) {
            items.push(JSON.parse(data));
          }
        }
      }
      return items;
    }

    try {
      return await this.db.getAll(storeName);
    } catch (error) {
      console.error(`Failed to get all from ${storeName}:`, error);
      // Fallback to localStorage
      const items: DatabaseSchema[T]['value'][] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${storeName}_`)) {
          const data = localStorage.getItem(key);
          if (data) {
            items.push(JSON.parse(data));
          }
        }
      }
      return items;
    }
  }

  async delete<T extends keyof DatabaseSchema>(
    storeName: T,
    id: string
  ): Promise<void> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${id}`;
      localStorage.removeItem(storageKey);
      return;
    }

    try {
      await this.db.delete(storeName, id);
    } catch (error) {
      console.error(`Failed to delete from ${storeName}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${storeName}_${id}`;
      localStorage.removeItem(storageKey);
    }
  }

  async clear<T extends keyof DatabaseSchema>(storeName: T): Promise<void> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage - remove all items for this store
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${storeName}_`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
      return;
    }

    try {
      await this.db.clear(storeName);
    } catch (error) {
      console.error(`Failed to clear ${storeName}:`, error);
      // Fallback to localStorage - remove all items for this store
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${storeName}_`)) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
    }
  }

  async query<T extends keyof DatabaseSchema>(
    storeName: T,
    indexName: string,
    value: any
  ): Promise<DatabaseSchema[T]['value'][]> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage - basic filtering
      const allItems = await this.getAll(storeName);
      return allItems.filter((item: any) => {
        const indexValue = item[indexName];
        if (Array.isArray(indexValue)) {
          return indexValue.includes(value);
        }
        return indexValue === value;
      });
    }

    try {
      const tx = this.db.transaction(storeName, 'readonly');
      const store = tx.objectStore(storeName);
      const index = store.index(indexName);
      return await index.getAll(value);
    } catch (error) {
      console.error(`Failed to query ${storeName} by ${indexName}:`, error);
      // Fallback to localStorage - basic filtering
      const allItems = await this.getAll(storeName);
      return allItems.filter((item: any) => {
        const indexValue = item[indexName];
        if (Array.isArray(indexValue)) {
          return indexValue.includes(value);
        }
        return indexValue === value;
      });
    }
  }

  async count<T extends keyof DatabaseSchema>(storeName: T): Promise<number> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      let count = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${storeName}_`)) {
          count++;
        }
      }
      return count;
    }

    try {
      return await this.db.count(storeName);
    } catch (error) {
      console.error(`Failed to count ${storeName}:`, error);
      // Fallback to localStorage
      let count = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${storeName}_`)) {
          count++;
        }
      }
      return count;
    }
  }

  // Backup and restore functionality
  async createBackup(name: string): Promise<string> {
    if (!this.db) throw new Error('Database not initialized');

    const backup = {
      id: `backup_${Date.now()}`,
      name,
      createdAt: new Date(),
      data: {
        prompts: await this.getAll('prompts'),
        agents: await this.getAll('agents'),
        results: await this.getAll('results'),
        settings: await this.getAll('settings'),
        nodeTemplates: await this.getAll('nodeTemplates'),
        chainFlows: await this.getAll('chainFlows'),
        communityVaultItems: await this.getAll('communityVaultItems') // Added vault items
      },
      size: 0
    };

    // Calculate backup size
    backup.size = JSON.stringify(backup.data).length;

    await this.add('backups', backup);
    return backup.id;
  }

  async restoreBackup(backupId: string): Promise<void> {
    const backup = await this.get('backups', backupId);
    if (!backup) throw new Error('Backup not found');

    // Clear existing data
    await this.clear('prompts');
    await this.clear('agents');
    await this.clear('results');
    await this.clear('settings');
    await this.clear('nodeTemplates');
    await this.clear('chainFlows');
    await this.clear('communityVaultItems'); // Clear vault items

    // Restore data
    const data = backup.data as any;
    
    for (const prompt of data.prompts || []) {
      await this.add('prompts', prompt);
    }
    
    for (const agent of data.agents || []) {
      await this.add('agents', agent);
    }
    
    for (const result of data.results || []) {
      await this.add('results', result);
    }
    
    for (const setting of data.settings || []) {
      await this.add('settings', setting);
    }

    for (const template of data.nodeTemplates || []) {
      await this.add('nodeTemplates', template);
    }

    for (const flow of data.chainFlows || []) {
      await this.add('chainFlows', flow);
    }

    for (const item of data.communityVaultItems || []) { // Added vault items
      await this.add('communityVaultItems', item);
    }
  }

  async getBackups(): Promise<DatabaseSchema['backups']['value'][]> {
    return this.getAll('backups');
  }

  async deleteBackup(backupId: string): Promise<void> {
    await this.delete('backups', backupId);
  }

  // Migration functionality
  async migrate(): Promise<void> {
    // Check if migration is needed
    const version = await this.getSetting('db_version');
    const currentVersion = this.version;

    if (!version || version.value < currentVersion) {
      console.log('Running database migration...');
      
      // Perform migration steps here
      await this.setSetting('db_version', currentVersion);
      
      console.log('Database migration completed');
    }
  }

  // Settings helpers
  async getSetting(key: string): Promise<DatabaseSchema['settings']['value'] | undefined> {
    const settings = await this.query('settings', 'key', key);
    return settings[0];
  }

  async setSetting(key: string, value: any): Promise<void> {
    const setting = {
      id: `setting_${key}`,
      key,
      value,
      updatedAt: new Date()
    };
    
    const existing = await this.getSetting(key);
    if (existing) {
      await this.update('settings', setting);
    } else {
      await this.add('settings', setting);
    }
  }

  // Export/Import functionality
  async exportData(): Promise<any> {
    return {
      prompts: await this.getAll('prompts'),
      agents: await this.getAll('agents'),
      results: await this.getAll('results'),
      settings: await this.getAll('settings'),
      nodeTemplates: await this.getAll('nodeTemplates'),
      chainFlows: await this.getAll('chainFlows'),
      communityVaultItems: await this.getAll('communityVaultItems'), // Added vault items
      exportedAt: new Date().toISOString(),
      version: this.version
    };
  }

  async importData(data: any): Promise<void> {
    // Validate data structure
    if (!data.prompts || !data.agents) {
      throw new Error('Invalid import data structure');
    }

    // Create backup before import
    await this.createBackup(`Pre-import backup ${new Date().toISOString()}`);

    // Clear existing data
    await this.clear('prompts');
    await this.clear('agents');
    await this.clear('results');
    // await this.clear('settings'); // Settings are usually not cleared on general data import
    await this.clear('nodeTemplates');
    await this.clear('chainFlows');
    await this.clear('communityVaultItems'); // Clear vault items

    // Import data
    for (const prompt of data.prompts) {
      await this.add('prompts', prompt);
    }
    
    for (const agent of data.agents) {
      await this.add('agents', agent);
    }
    
    for (const result of data.results || []) {
      await this.add('results', result);
    }

    for (const template of data.nodeTemplates || []) {
      await this.add('nodeTemplates', template);
    }

    for (const flow of data.chainFlows || []) {
      await this.add('chainFlows', flow);
    }

    for (const item of data.communityVaultItems || []) { // Added vault items
      await this.add('communityVaultItems', item);
    }
  }

  // Database statistics
  async getStats(): Promise<{
    prompts: number;
    agents: number;
    results: number;
    nodeTemplates: number;
    chainFlows: number;
    communityVaultItems: number; // Added vault items
    backups: number;
    totalSize: number;
  }> {
    const [prompts, agents, results, nodeTemplates, chainFlows, communityVaultItems, backups] = await Promise.all([
      this.count('prompts'),
      this.count('agents'),
      this.count('results'),
      this.count('nodeTemplates'),
      this.count('chainFlows'),
      this.count('communityVaultItems'), // Added vault items
      this.count('backups')
    ]);

    // Estimate total size (rough calculation)
    const allData = await this.exportData();
    const totalSize = JSON.stringify(allData).length;

    return {
      prompts,
      agents,
      results,
      nodeTemplates,
      chainFlows,
      communityVaultItems, // Added vault items
      backups,
      totalSize
    };
  }

  // Cleanup old data
  async cleanup(daysToKeep: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    // Clean up old results
    const allResults = await this.getAll('results');
    const oldResults = allResults.filter(result => 
      new Date(result.timestamp) < cutoffDate
    );

    for (const result of oldResults) {
      await this.delete('results', result.id);
    }

    // Clean up old backups (keep last 5)
    const allBackups = await this.getAll('backups');
    const sortedBackups = allBackups.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const backupsToDelete = sortedBackups.slice(5);
    for (const backup of backupsToDelete) {
      await this.delete('backups', backup.id);
    }
  }

  // Add save method for compatibility with database-fix.ts
  async save<T extends keyof DatabaseSchema>(store: T, data: DatabaseSchema[T]['value']): Promise<void> {
    return this.update(store, data);
  }

  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export const databaseService = new DatabaseService();
