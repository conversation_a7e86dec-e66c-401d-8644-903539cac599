
import { useState, useMemo } from 'react';
import { usePromptStore, VaultItemSummary, VaultItemType } from '@/hooks/usePromptStore';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Search, Tag, Layers, FileText, Bookmark, ArrowDownUp, CalendarDays, UserCircle } from 'lucide-react';

// Helper to get an icon for each vault item type
const getTypeIcon = (type: VaultItemType) => {
  switch (type) {
    case 'prompt_pack': return <Layers className="w-4 h-4 mr-2 text-blue-400" />;
    case 'flow_blueprint': return <FileText className="w-4 h-4 mr-2 text-green-400" />;
    case 'node_template': return <Bookmark className="w-4 h-4 mr-2 text-yellow-400" />;
    default: return <Layers className="w-4 h-4 mr-2 text-gray-400" />;
  }
};

export const CommunityVaultView = () => {
  const { getCommunityVaultItems, unpublishItemFromVault } = usePromptStore();
  const allItems = getCommunityVaultItems(); // Already sorted by date descending

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<VaultItemType | 'all'>('all');
  const [sortBy, setSortBy] = useState<'createdAt_desc' | 'createdAt_asc' | 'name_asc' | 'name_desc'>('createdAt_desc');

  const [selectedItemForPreview, setSelectedItemForPreview] = useState<VaultItemSummary | null>(null);

  const allAvailableTags = useMemo(() => {
    const tagsSet = new Set<string>();
    allItems.forEach(item => item.tags?.forEach(tag => tagsSet.add(tag)));
    return Array.from(tagsSet).sort();
  }, [allItems]);

  const filteredAndSortedItems = useMemo(() => {
    let items = [...allItems];

    // Filter by search term (name or description)
    if (searchTerm) {
      items = items.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (selectedType !== 'all') {
      items = items.filter(item => item.type === selectedType);
    }

    // Filter by tags (must contain ALL selected tags)
    if (selectedTags.length > 0) {
      items = items.filter(item =>
        selectedTags.every(selTag => item.tags?.includes(selTag))
      );
    }

    // Sort
    items.sort((a, b) => {
      switch (sortBy) {
        case 'name_asc': return a.name.localeCompare(b.name);
        case 'name_desc': return b.name.localeCompare(a.name);
        case 'createdAt_asc': return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'createdAt_desc':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    return items;
  }, [allItems, searchTerm, selectedType, selectedTags, sortBy]);

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]
    );
  };

  const handleUnpublish = async (vaultId: string, itemName: string) => {
    if (window.confirm(`Are you sure you want to unpublish "${itemName}" from the local vault?`)) {
      try {
        await unpublishItemFromVault(vaultId);
        // The list will re-render due to store update
      } catch (error) {
        // toast error
        console.error("Failed to unpublish", error);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold text-slate-100">Community Vault (Local Mock)</h2>
        {/* Potential future add button? <Button>Submit New</Button> */}
      </div>
      <p className="text-slate-400">Browse items published to your local vault. In a real system, this would be a shared community space.</p>

      {/* Filters and Search */}
      <Card className="p-4 bg-slate-800/50 border-slate-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <Label htmlFor="vault-search" className="text-sm text-slate-300">Search by Name/Description</Label>
            <div className="relative mt-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
              <Input
                id="vault-search"
                placeholder="Search items..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-9 bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="vault-type-filter" className="text-sm text-slate-300">Filter by Type</Label>
            <Select value={selectedType} onValueChange={(value) => setSelectedType(value as VaultItemType | 'all')}>
              <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="prompt_pack">Prompt Packs</SelectItem>
                <SelectItem value="flow_blueprint">Flow Blueprints</SelectItem>
                <SelectItem value="node_template">Node Templates</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="vault-sort-by" className="text-sm text-slate-300">Sort By</Label>
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Sort by..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt_desc">Date Published (Newest)</SelectItem>
                <SelectItem value="createdAt_asc">Date Published (Oldest)</SelectItem>
                <SelectItem value="name_asc">Name (A-Z)</SelectItem>
                <SelectItem value="name_desc">Name (Z-A)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        {allAvailableTags.length > 0 && (
          <div className="mt-4 pt-3 border-t border-slate-700/50">
            <Label className="text-sm text-slate-300 mb-2 block">Filter by Tags</Label>
            <div className="flex flex-wrap gap-2">
              {allAvailableTags.map(tag => (
                <Button
                  key={tag}
                  variant={selectedTags.includes(tag) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleTagToggle(tag)}
                  className={selectedTags.includes(tag)
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'border-slate-600 text-slate-300 hover:bg-slate-700'}
                >
                  <Tag className="w-3 h-3 mr-1.5" /> {tag}
                </Button>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Items Grid/List */}
      {filteredAndSortedItems.length === 0 && (
        <p className="text-slate-500 text-center py-10">No items match your current filters.</p>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAndSortedItems.map(item => (
          <Card key={item.vaultId} className="bg-slate-800/50 border-slate-700 flex flex-col">
            <CardHeader>
              <CardTitle className="text-lg text-slate-100 flex items-center gap-2">
                {getTypeIcon(item.type)} {item.name}
              </CardTitle>
              <CardDescription className="text-xs text-slate-400">
                By {item.author || 'Unknown Author'} | Published: {new Date(item.createdAt).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-slate-300 mb-2 line-clamp-3">{item.description || 'No description.'}</p>
              {item.previewSnippet && <p className="text-xs text-slate-400 italic bg-slate-700/50 p-2 rounded line-clamp-2">Preview: {item.previewSnippet}</p>}
              {item.tags && item.tags.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-1">
                  {item.tags.map(tag => <Badge key={tag} variant="secondary" className="text-xs bg-slate-700 text-slate-300">{tag}</Badge>)}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between items-center pt-3 border-t border-slate-700/50">
              <div className="text-xs text-slate-500">
                {item.upvotes || 0} Upvotes | {item.forksCount || 0} Forks
              </div>
              <Button size="sm" variant="ghost" onClick={() => setSelectedItemForPreview(item)} className="text-blue-400 hover:text-blue-300">
                View Details
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Preview Modal */}
      <Dialog open={!!selectedItemForPreview} onOpenChange={() => setSelectedItemForPreview(null)}>
        <DialogContent className="sm:max-w-lg bg-slate-800 border-slate-700 text-slate-200">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedItemForPreview?.name} {selectedItemForPreview && getTypeIcon(selectedItemForPreview.type)}
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              By {selectedItemForPreview?.author || 'Unknown'} | Published: {selectedItemForPreview ? new Date(selectedItemForPreview.createdAt).toLocaleString() : ''}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-3 max-h-[60vh] overflow-y-auto">
            <p><strong>Description:</strong> {selectedItemForPreview?.description || 'N/A'}</p>
            <p><strong>Type:</strong> {selectedItemForPreview?.type.replace('_', ' ')}</p>
            {selectedItemForPreview?.previewSnippet && <p><strong>Preview:</strong> <span className="italic text-slate-300">{selectedItemForPreview.previewSnippet}</span></p>}
            {selectedItemForPreview?.tags && <p><strong>Tags:</strong> {selectedItemForPreview.tags.join(', ')}</p>}
            {selectedItemForPreview?.itemCounts && (
              <div>
                <strong>Contents:</strong>
                <ul className="list-disc list-inside ml-4 text-sm">
                  {Object.entries(selectedItemForPreview.itemCounts).map(([key, value]) =>
                    value ? <li key={key}>{key.charAt(0).toUpperCase() + key.slice(1)}: {value}</li> : null
                  )}
                </ul>
              </div>
            )}
            <p className="text-slate-500 text-xs mt-2">Vault ID: {selectedItemForPreview?.vaultId}</p>
            <p className="text-slate-500 text-xs">Original Item ID: {selectedItemForPreview?.id}</p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
