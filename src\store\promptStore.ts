// =================================================================================================
// PROMPT STORE - Custom Class-based State Management
// =================================================================================================

import { databaseService } from '@/lib/database';
import { agentSimulationService, AgentSimulationRequest, SimulationResult as SingleAgentSimulationResult, AgentResponse as SingleAgentResponse } from '@/lib/agent-simulation';
import type { ImportedPromptStudioData } from '@/lib/import-tools';
// Import types from chain-linker to avoid duplication
import type { ChainFlow, ChainNode, ChainConnection } from '@/lib/chain-linker';

interface Agent {
  id: string;
  name: string;
  role: string;
  systemPrompt: string;
  personality: string;
  enabled: boolean;
  expertise?: string[];
  avatar?: string;
  modelConfig?: {
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };
}

interface PromptVariant {
  id: string;
  name: string;
  prompt: string;
  systemPrompt?: string;
  variables: Record<string, string>;
  purpose?: string;
  whenToUse?: string;
  whenNotToUse?: string;
  experimentalSubVariants?: string[];
  buildKit?: {
    frame: string;
    example: string;
  };
  compatibleAgents?: string[];
  parentId?: string;
  version?: number;
  lineageId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  promptType?: string;
  definedOutputFormat?: string;
  tags?: string[];
  usageNotes?: string;
  targetModels?: string[];
  desiredTone?: string;
  contextualDepth?: string;
  expectedOutputFormat?: string;
  lastPreviewSnapshot?: {
    output: string;
    metadata: any;
    variablesUsed: Record<string, string>;
    timestamp: string;
  };
}

export interface TestRunExecutionMetadata {
  testGoal?: string;
  inputSample?: string;
  expectedOutputFormat?: string;
  estimatedTokens?: number;
}

export interface NodeTemplate {
  id: string;
  name: string;
  description?: string;
  nodeType: 'prompt' | 'agent' | 'condition' | 'output' | 'input' | string;
  nodeData: any;
  createdAt: Date;
  tags?: string[];
}

export type VaultItemType = 'prompt_pack' | 'flow_blueprint' | 'node_template';

export interface VaultItemSummary {
  id: string;
  vaultId: string;
  type: VaultItemType;
  name: string;
  description?: string;
  author?: string;
  tags?: string[];
  createdAt: Date;
  upvotes?: number;
  forksCount?: number;
  previewSnippet?: string;
  itemCounts?: { prompts?: number; agents?: number; results?: number; flows?: number; };
}

interface TestResult {
  id: string;
  timestamp: Date;
  type: 'prompt_test' | 'agent_simulation' | 'variation_testing' | 'project_simulation' | 'integrated_test';
  data: any;
  runMetadata?: TestRunExecutionMetadata;
  scores?: {
    fidelity?: number;
    adherence?: number;
    consistency?: number;
    creativity?: number;
    accuracy?: number;
  };
}

interface InteractionDetail {
  promptId: string;
  promptName: string;
  agentId: string;
  agentName: string;
  response?: string;
  confidence?: number;
  sentiment?: 'positive' | 'neutral' | 'constructive' | 'critical';
  keyInsights?: string[];
  reasoning?: string;
  error?: string;
  aiResponseSnapshot?: {
    content: string;
    usage?: { promptTokens: number; completionTokens: number; totalTokens: number };
    model: string;
    provider: string;
  };
}

class PromptStore {
  private agents: Agent[] = [];
  private prompts: PromptVariant[] = [];
  private results: TestResult[] = [];
  private nodeTemplates: NodeTemplate[] = [];
  private chainFlows: ChainFlow[] = [];
  private communityVaultItems: VaultItemSummary[] = [];
  private listeners: (() => void)[] = [];
  private initialized = false;

  private defaultAgents: Agent[] = [
    {
      id: 'default-researcher',
      name: 'Research Analyst',
      role: 'researcher',
      systemPrompt: 'You are a thorough research analyst who provides comprehensive, well-sourced insights.',
      personality: 'analytical and methodical',
      enabled: true,
      expertise: ['research', 'analysis', 'data interpretation']
    },
    {
      id: 'default-creative',
      name: 'Creative Writer',
      role: 'writer',
      systemPrompt: 'You are a creative writer who crafts engaging, imaginative content.',
      personality: 'creative and expressive',
      enabled: true,
      expertise: ['writing', 'storytelling', 'content creation']
    }
  ];

  async initialize(): Promise<void> {
    if (this.initialized) return;
    try {
      await databaseService.initialize();
      await this.loadFromDatabase();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize database, using memory storage:', error);
      this.agents = [...this.defaultAgents];
      this.prompts = [];
      this.results = [];
      this.nodeTemplates = [];
      this.chainFlows = [];
      this.communityVaultItems = [];
      this.initialized = true;
    }
  }

  private async loadFromDatabase(): Promise<void> {
    try {
      const [agents, prompts, results, nodeTemplates, chainFlows, communityVaultItems] = await Promise.all([
        databaseService.getAll('agents'),
        databaseService.getAll('prompts'),
        databaseService.getAll('results'),
        databaseService.getAll('nodeTemplates'),
        databaseService.getAll('chainFlows'),
        databaseService.getAll('communityVaultItems')
      ]);

      this.agents = agents.length > 0 ? agents : [...this.defaultAgents];
      this.prompts = prompts || [];
      this.results = results || [];
      this.nodeTemplates = nodeTemplates || [];
      this.chainFlows = chainFlows || [];
      this.communityVaultItems = communityVaultItems || [];

      if (agents.length === 0 && this.defaultAgents.length > 0) {
        for (const agent of this.defaultAgents) {
          await databaseService.add('agents', agent);
        }
      }
    } catch (error) {
      console.error('Failed to load from database:', error);
      this.agents = [...this.defaultAgents];
      this.prompts = [];
      this.results = [];
      this.nodeTemplates = [];
      this.chainFlows = [];
      this.communityVaultItems = [];
    }
  }

  // Agent methods
  getAgents(): Agent[] { return [...this.agents]; }
  getEnabledAgents(): Agent[] { return this.agents.filter(agent => agent.enabled); }
  
  async addAgent(agent: Omit<Agent, 'id'>): Promise<Agent> {
    const newAgent: Agent = { ...agent, id: crypto.randomUUID() };
    this.agents.push(newAgent);
    try { 
      await databaseService.add('agents', newAgent); 
    } catch (error) { 
      console.error('Failed to save agent:', error);
      this.agents = this.agents.filter(a => a.id !== newAgent.id);
      throw error;
    }
    this.notifyListeners(); 
    return newAgent;
  }

  async updateAgent(id: string, updates: Partial<Agent>): Promise<void> {
    const index = this.agents.findIndex(a => a.id === id);
    if (index !== -1) {
      const original = this.agents[index];
      this.agents[index] = { ...original, ...updates };
      try { 
        await databaseService.update('agents', this.agents[index]); 
      } catch (error) { 
        console.error('Failed to update agent:', error); 
        this.agents[index] = original; 
        throw error;
      }
      this.notifyListeners();
    }
  }

  async deleteAgent(id: string): Promise<void> {
    const agentToDelete = this.agents.find(a => a.id === id);
    this.agents = this.agents.filter(a => a.id !== id);
    try { 
      await databaseService.delete('agents', id); 
    } catch (error) { 
      console.error('Failed to delete agent:', error);
      if (agentToDelete) this.agents.push(agentToDelete);
      throw error;
    }
    this.notifyListeners();
  }

  async toggleAgent(id: string): Promise<void> {
    const agent = this.agents.find(a => a.id === id);
    if (agent) {
      await this.updateAgent(id, { enabled: !agent.enabled });
    }
  }

  // Prompt methods
  getPrompts(): PromptVariant[] { return [...this.prompts]; }
  
  async addPrompt(promptData: Omit<PromptVariant, 'id' | 'version' | 'lineageId' | 'createdAt' | 'updatedAt'>): Promise<PromptVariant> {
    const now = new Date();
    const newId = crypto.randomUUID();
    const newPrompt: PromptVariant = { 
      ...promptData, 
      id: newId, 
      parentId: undefined, 
      version: 1, 
      lineageId: newId, 
      createdAt: now, 
      updatedAt: now 
    };
    this.prompts.push(newPrompt);
    try { 
      await databaseService.add('prompts', newPrompt); 
    } catch (error) { 
      console.error('Failed to save prompt:', error); 
      this.prompts = this.prompts.filter(p => p.id !== newId); 
      throw error; 
    }
    this.notifyListeners(); 
    return newPrompt;
  }

  async updatePrompt(id: string, updates: Partial<PromptVariant>): Promise<void> {
    const index = this.prompts.findIndex(p => p.id === id);
    if (index !== -1) {
      const original = this.prompts[index];
      this.prompts[index] = { ...original, ...updates, updatedAt: new Date() };
      try { 
        await databaseService.update('prompts', this.prompts[index]); 
      } catch (error) { 
        console.error('Failed to update prompt:', error); 
        this.prompts[index] = original; 
        throw error;
      }
      this.notifyListeners();
    }
  }

  async forkPrompt(originalPromptId: string, newName?: string): Promise<PromptVariant> {
    const originalPrompt = this.prompts.find(p => p.id === originalPromptId);
    if (!originalPrompt) throw new Error(`Prompt with ID ${originalPromptId} not found.`);
    
    const { id, parentId, version, name, createdAt, updatedAt, ...originalDataToCopy } = originalPrompt;
    const forkedId = crypto.randomUUID();
    const forkedPrompt: PromptVariant = {
      ...JSON.parse(JSON.stringify(originalDataToCopy)), 
      id: forkedId, 
      name: newName || `${originalPrompt.name} - Fork`,
      parentId: originalPrompt.id, 
      version: 1, 
      lineageId: originalPrompt.lineageId || originalPrompt.id,
      createdAt: new Date(), 
      updatedAt: new Date(),
    };
    
    this.prompts.push(forkedPrompt);
    try { 
      await databaseService.add('prompts', forkedPrompt); 
    } catch (error) { 
      console.error('Failed to save forked prompt:', error); 
      this.prompts = this.prompts.filter(p => p.id !== forkedId); 
      throw error; 
    }
    this.notifyListeners(); 
    return forkedPrompt;
  }

  async deletePrompt(id: string): Promise<void> {
    const promptToDelete = this.prompts.find(p => p.id === id);
    this.prompts = this.prompts.filter(p => p.id !== id);
    try { 
      await databaseService.delete('prompts', id); 
    } catch (error) { 
      console.error('Failed to delete prompt:', error);
      if (promptToDelete) this.prompts.push(promptToDelete);
      throw error;
    }
    this.notifyListeners();
  }

  // Result methods
  getResults(): TestResult[] { 
    return [...this.results].sort((a,b) => b.timestamp.getTime() - a.timestamp.getTime()); 
  }
  
  async addResult(resultData: Omit<TestResult, 'id'>): Promise<TestResult> {
    const newResultWithId: TestResult = { ...resultData, id: crypto.randomUUID() };
    this.results.unshift(newResultWithId);
    try { 
      await databaseService.add('results', newResultWithId); 
    } catch (error) { 
      console.error('Failed to save result:', error); 
    }
    this.notifyListeners(); 
    return newResultWithId;
  }

  async clearResults(): Promise<void> {
    this.results = [];
    try {
      await databaseService.clear('results');
    } catch (error) {
      console.error('Failed to clear results:', error);
    }
    this.notifyListeners();
  }

  // NodeTemplate methods
  getNodeTemplates(): NodeTemplate[] { return [...this.nodeTemplates]; }
  
  async addNodeTemplate(templateData: Omit<NodeTemplate, 'id' | 'createdAt'>): Promise<NodeTemplate> {
    const newTemplate: NodeTemplate = { 
      ...templateData, 
      id: crypto.randomUUID(), 
      createdAt: new Date() 
    };
    this.nodeTemplates.push(newTemplate);
    try { 
      await databaseService.add('nodeTemplates', newTemplate); 
    } catch (error) { 
      console.error('Failed to save template:', error); 
      this.nodeTemplates = this.nodeTemplates.filter(t => t.id !== newTemplate.id); 
      throw error; 
    }
    this.notifyListeners(); 
    return newTemplate;
  }

  async deleteNodeTemplate(templateId: string): Promise<void> {
    const templateToDelete = this.nodeTemplates.find(t => t.id === templateId);
    this.nodeTemplates = this.nodeTemplates.filter(t => t.id !== templateId);
    try { 
      await databaseService.delete('nodeTemplates', templateId); 
    } catch (error) { 
      console.error('Failed to delete template:', error);
      if (templateToDelete) this.nodeTemplates.push(templateToDelete);
      throw error;
    }
    this.notifyListeners();
  }

  // ChainFlow methods
  getChainFlows(): ChainFlow[] { return [...this.chainFlows]; }
  
  async addChainFlow(flowData: Omit<ChainFlow, 'id' | 'metadata'> & { metadata?: Partial<ChainFlow['metadata']> }): Promise<ChainFlow> {
    const now = new Date();
    const newFlow: ChainFlow = { 
      nodes: [], 
      connections: [], 
      ...flowData,
      description: flowData.description || '', // Ensure description is provided
      id: crypto.randomUUID(), 
      metadata: { 
        version: '1.0.0', 
        tags: [], 
        createdAt: now, 
        updatedAt: now,
        ...flowData.metadata 
      } 
    };
    this.chainFlows.push(newFlow);
    try { 
      await databaseService.add('chainFlows', newFlow); 
    } catch (error) { 
      console.error('Failed to save flow:', error); 
      this.chainFlows = this.chainFlows.filter(f => f.id !== newFlow.id); 
      throw error; 
    }
    this.notifyListeners(); 
    return newFlow;
  }

  async updateChainFlow(flowId: string, updates: Partial<Omit<ChainFlow, 'id' | 'metadata'>> & { metadata?: Partial<ChainFlow['metadata']> }): Promise<ChainFlow | undefined> {
    const flowIndex = this.chainFlows.findIndex(f => f.id === flowId);
    if (flowIndex === -1) return undefined;
    
    const originalFlow = this.chainFlows[flowIndex];
    const updatedFlow: ChainFlow = { 
      ...originalFlow, 
      ...updates,
      description: updates.description || originalFlow.description || '', // Ensure description exists
      metadata: { 
        ...originalFlow.metadata, 
        ...updates.metadata, 
        updatedAt: new Date() 
      } 
    };
    this.chainFlows[flowIndex] = updatedFlow;
    
    try { 
      await databaseService.update('chainFlows', updatedFlow); 
    } catch (error) { 
      console.error('Failed to update flow:', error); 
      this.chainFlows[flowIndex] = originalFlow; 
      throw error; 
    }
    this.notifyListeners(); 
    return updatedFlow;
  }

  async deleteChainFlow(flowId: string): Promise<void> {
    const flowToDelete = this.chainFlows.find(f => f.id === flowId);
    this.chainFlows = this.chainFlows.filter(f => f.id !== flowId);
    try { 
      await databaseService.delete('chainFlows', flowId); 
    } catch (error) { 
      console.error('Failed to delete flow:', error);
      if (flowToDelete) this.chainFlows.push(flowToDelete);
      throw error;
    }
    this.notifyListeners();
  }

  // Community Vault Methods
  getCommunityVaultItems(): VaultItemSummary[] {
    return [...this.communityVaultItems].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  async publishItemToVault(itemData: Omit<VaultItemSummary, 'vaultId' | 'createdAt'>): Promise<VaultItemSummary> {
    const newVaultItem: VaultItemSummary = { 
      ...itemData, 
      vaultId: crypto.randomUUID(), 
      createdAt: new Date(), 
      upvotes: itemData.upvotes || 0, 
      forksCount: itemData.forksCount || 0 
    };
    this.communityVaultItems.push(newVaultItem);
    try { 
      await databaseService.add('communityVaultItems', newVaultItem); 
    } catch (error) { 
      console.error('Failed to save vault item:', error); 
      this.communityVaultItems = this.communityVaultItems.filter(item => item.vaultId !== newVaultItem.vaultId); 
      throw error; 
    }
    this.notifyListeners(); 
    return newVaultItem;
  }

  async unpublishItemFromVault(vaultId: string): Promise<void> {
    const itemToDelete = this.communityVaultItems.find(item => item.vaultId === vaultId);
    this.communityVaultItems = this.communityVaultItems.filter(item => item.vaultId !== vaultId);
    try { 
      await databaseService.delete('communityVaultItems', vaultId); 
    } catch (error) { 
      console.error('Failed to delete vault item:', error);
      if (itemToDelete) this.communityVaultItems.push(itemToDelete);
      throw error;
    }
    this.notifyListeners();
  }

  // Compatibility and Integration methods
  getCompatibleAgents(promptId: string): Agent[] {
    const prompt = this.prompts.find(p => p.id === promptId);
    if (!prompt?.compatibleAgents?.length) {
      return this.getEnabledAgents();
    }
    return this.agents.filter(agent => 
      agent.enabled && prompt.compatibleAgents!.includes(agent.id)
    );
  }

  async runIntegratedTest(
    promptIds: string[], 
    agentIds: string[], 
    scenario: string, 
    testGoal?: string
  ): Promise<TestResult> {
    const prompts = promptIds.map(id => this.prompts.find(p => p.id === id)).filter(Boolean) as PromptVariant[];
    const agents = agentIds.map(id => this.agents.find(a => a.id === id)).filter(Boolean) as Agent[];
    
    const request: AgentSimulationRequest = {
      scenario,
      agents: agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        role: agent.role,
        systemPrompt: agent.systemPrompt,
        personality: agent.personality,
        enabled: agent.enabled,
        expertise: agent.expertise || []
      }))
    };

    try {
      const simulation = await agentSimulationService.runSimulation(request);
      
      const result: TestResult = {
        id: crypto.randomUUID(),
        timestamp: new Date(),
        type: 'integrated_test',
        data: {
          simulation,
          promptIds,
          agentIds,
          scenario,
          interactions: simulation.agentResponses?.map(response => ({
            promptId: prompts[0]?.id || '',
            promptName: prompts[0]?.name || '',
            agentId: response.agentId,
            agentName: response.agentName,
            response: response.response,
            confidence: response.confidence,
            sentiment: response.sentiment,
            keyInsights: response.keyInsights,
            reasoning: response.reasoning
          })) || []
        },
        runMetadata: {
          testGoal,
          inputSample: scenario,
          expectedOutputFormat: 'Agent simulation responses',
          estimatedTokens: simulation.metadata?.totalTokens || 0
        }
      };

      return await this.addResult(result);
    } catch (error) {
      console.error('Integrated test failed:', error);
      throw error;
    }
  }

  // Import method for PromptX data
  async importPromptXData(data: ImportedPromptStudioData): Promise<{ successCount: number; errorCount: number; errors: {type: string, id?: string | number, message: string}[] }> {
    let successCount = 0;
    let errorCount = 0;
    const errorsReport: {type: string, id?: string | number, message: string}[] = [];

    if (data.prompts) {
      for (const prompt of data.prompts) {
        try {
          const { id, version, lineageId, createdAt, updatedAt, ...promptData } = prompt;
          await this.addPrompt(promptData);
          successCount++;
        } catch (e: any) {
          errorCount++;
          errorsReport.push({ type: 'PromptVariant', id: prompt.id, message: e.message });
        }
      }
    }

    if (data.agents) {
      for (const agent of data.agents) {
        try {
          const { id, ...agentData } = agent;
          await this.addAgent(agentData);
          successCount++;
        } catch (e: any) {
          errorCount++;
          errorsReport.push({ type: 'Agent', id: agent.id, message: e.message });
        }
      }
    }

    if (data.results) {
      for (const result of data.results) {
        try {
          const { id, ...resultData } = result;
          await this.addResult(resultData);
          successCount++;
        } catch (e: any) {
          errorCount++;
          errorsReport.push({ type: 'TestResult', id: result.id, message: e.message });
        }
      }
    }

    if (data.nodeTemplates) {
      for (const template of data.nodeTemplates) {
        try {
          const { id, createdAt, ...templateData } = template;
          await this.addNodeTemplate(templateData);
          successCount++;
        } catch (e: any) {
          errorCount++;
          errorsReport.push({ type: 'NodeTemplate', id: template.id, message: e.message });
        }
      }
    }

    if (data.flows) {
      for (const flow of data.flows) {
        try {
          const { id, metadata, ...flowData } = flow;
          await this.addChainFlow({ ...flowData, metadata, description: flowData.description || '' });
          successCount++;
        } catch (e: any) {
          errorCount++;
          errorsReport.push({ type: 'ChainFlow', id: flow.id, message: e.message });
        }
      }
    }

    this.notifyListeners();
    return { successCount, errorCount, errors: errorsReport };
  }

  // Subscription methods
  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Database utilities
  async createBackup(name: string): Promise<string> { 
    return databaseService.createBackup(name); 
  }
  
  async restoreBackup(backupId: string): Promise<void> { 
    await databaseService.restoreBackup(backupId); 
    await this.loadFromDatabase(); 
  }
  
  async getBackups(): Promise<any[]> { 
    return databaseService.getBackups(); 
  }
  
  async deleteBackup(backupId: string): Promise<void> { 
    return databaseService.deleteBackup(backupId); 
  }
  
  async exportData(): Promise<any> { 
    return databaseService.exportData(); 
  }
  
  async importData(data: any): Promise<void> { 
    await databaseService.importData(data); 
    await this.loadFromDatabase();
  }
  
  async getStats(): Promise<any> { 
    try { 
      return await databaseService.getStats(); 
    } catch(e) { 
      return {
        prompts: this.prompts.length,
        agents: this.agents.length,
        results: this.results.length,
        nodeTemplates: this.nodeTemplates.length,
        chainFlows: this.chainFlows.length,
        communityVaultItems: this.communityVaultItems.length
      }; 
    } 
  }
  
  async cleanup(daysToKeep: number = 30): Promise<void> { 
    await databaseService.cleanup(daysToKeep); 
    await this.loadFromDatabase(); 
  }
}

export const promptStore = new PromptStore();

// Export types
export type { Agent, PromptVariant, TestResult, ChainFlow, ChainNode, ChainConnection };
