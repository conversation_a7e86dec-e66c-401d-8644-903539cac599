
import { vi, describe, it, expect, beforeEach, afterEach, type Mock } from 'vitest';
import { promptStore, PromptVariant, Agent } from '../promptStore';
import { databaseService } from '@/lib/database';
import { agentSimulationService } from '@/lib/agent-simulation';

// Mock databaseService
vi.mock('@/lib/database', () => ({
  databaseService: {
    initialize: vi.fn().mockResolvedValue(undefined),
    getAll: vi.fn().mockImplementation(async (storeName: string) => {
      if (storeName === 'prompts') return mockDbPrompts;
      if (storeName === 'agents') return mockDbAgents;
      if (storeName === 'results') return [];
      return [];
    }),
    add: vi.fn().mockResolvedValue(undefined),
    update: vi.fn().mockResolvedValue(undefined),
    delete: vi.fn().mockResolvedValue(undefined),
  }
}));

// Mock agentSimulationService
vi.mock('@/lib/agent-simulation', () => ({
  agentSimulationService: {
    runSimulation: vi.fn()
  }
}));

let mockDbPrompts: PromptVariant[] = [];
let mockDbAgents: Agent[] = [];

describe('PromptStore', () => {
  beforeEach(async () => {
    // Reset mock DB state
    mockDbPrompts = [];
    mockDbAgents = [];

    // Reset and re-initialize the store for each test to ensure isolation
    promptStore['prompts'] = [];
    promptStore['agents'] = [];
    promptStore['results'] = [];
    promptStore['listeners'] = [];
    promptStore['initialized'] = false;
    await promptStore.initialize();

    vi.clearAllMocks();
  });

  describe('Prompt Operations', () => {
    const samplePromptData: Omit<PromptVariant, 'id' | 'version' | 'lineageId' | 'createdAt' | 'updatedAt'> = {
      name: 'Original Prompt',
      prompt: 'This is the original prompt text: {{variable}}.',
      systemPrompt: 'System prompt for original.',
      variables: { variable: 'testValue' },
      purpose: 'To be forked.',
    };

    it('addPrompt should initialize lineageId and version for a new prompt', async () => {
      const addedPrompt = await promptStore.addPrompt(samplePromptData);
      expect(addedPrompt.version).toBe(1);
      expect(addedPrompt.lineageId).toBe(addedPrompt.id);
      expect(addedPrompt.parentId).toBeUndefined();
      expect(addedPrompt.createdAt).toBeInstanceOf(Date);
      expect(addedPrompt.updatedAt).toBeInstanceOf(Date);
      expect(databaseService.add).toHaveBeenCalledWith('prompts', expect.objectContaining(addedPrompt));
    });

    describe('forkPrompt', () => {
      let originalPrompt: PromptVariant;

      beforeEach(async () => {
        originalPrompt = await promptStore.addPrompt(samplePromptData);
        vi.clearAllMocks();
      });

      it('should correctly fork a prompt with a new ID, parentId, lineageId, and version', async () => {
        const forkedName = 'Forked Prompt Name';
        const forkedPrompt = await promptStore.forkPrompt(originalPrompt.id, forkedName);

        expect(forkedPrompt.id).not.toBe(originalPrompt.id);
        expect(forkedPrompt.parentId).toBe(originalPrompt.id);
        expect(forkedPrompt.lineageId).toBe(originalPrompt.lineageId);
        expect(forkedPrompt.version).toBe(1);
        expect(forkedPrompt.name).toBe(forkedName);
        expect(forkedPrompt.createdAt).toBeInstanceOf(Date);
        expect(forkedPrompt.updatedAt).toBeInstanceOf(Date);
        expect(forkedPrompt.createdAt!.getTime()).toBeGreaterThanOrEqual(originalPrompt.createdAt!.getTime());

        expect(forkedPrompt.prompt).toBe(originalPrompt.prompt);
        expect(forkedPrompt.variables).toEqual(originalPrompt.variables);
        expect(forkedPrompt.variables).not.toBe(originalPrompt.variables);

        expect(databaseService.add).toHaveBeenCalledTimes(1);
        expect(databaseService.add).toHaveBeenCalledWith('prompts', expect.objectContaining(forkedPrompt));

        const promptsInStore = promptStore.getPrompts();
        expect(promptsInStore.find(p => p.id === forkedPrompt.id)).toBeDefined();
      });

      it('should use default naming if newName is not provided for fork', async () => {
        const forkedPrompt = await promptStore.forkPrompt(originalPrompt.id);
        expect(forkedPrompt.name).toBe(`${originalPrompt.name} - Fork`);
      });

      it('should handle forking a prompt that itself has no lineageId (older data)', async () => {
        const originalPromptNoLineage = { ...originalPrompt };
        delete originalPromptNoLineage.lineageId;
        promptStore['prompts'] = promptStore['prompts'].map(p => p.id === originalPrompt.id ? originalPromptNoLineage : p);

        const forkedPrompt = await promptStore.forkPrompt(originalPrompt.id);
        expect(forkedPrompt.lineageId).toBe(originalPrompt.id);
      });

      it('should throw an error if the original prompt to fork is not found', async () => {
        await expect(promptStore.forkPrompt('non_existent_id', 'Test Fork'))
          .rejects.toThrow('Prompt with ID non_existent_id not found.');
        expect(databaseService.add).not.toHaveBeenCalled();
      });

      it('should notify listeners after forking a prompt', async () => {
        const listenerMock = vi.fn();
        promptStore.subscribe(listenerMock);
        await promptStore.forkPrompt(originalPrompt.id);
        expect(listenerMock).toHaveBeenCalledTimes(1);
      });
    });
  });

  it('addPrompt should save new PromptVariant fields if provided', async () => {
    const promptData = {
      name: 'Metadata Test Prompt',
      prompt: 'Test prompt content',
      variables: {},
      purpose: 'To test new fields',
      promptType: 'test_type',
      definedOutputFormat: 'json_array',
      tags: ['metadata', 'new'],
      usageNotes: 'Ensure all new fields are saved.',
      targetModels: ['gpt-4o', 'test-model-1']
    };
    const addedPrompt = await promptStore.addPrompt(promptData);

    const retrievedPrompt = promptStore.getPrompts().find(p => p.id === addedPrompt.id);
    expect(retrievedPrompt).toBeDefined();
    expect(retrievedPrompt?.promptType).toBe('test_type');
    expect(retrievedPrompt?.definedOutputFormat).toBe('json_array');
    expect(retrievedPrompt?.tags).toEqual(['metadata', 'new']);
    expect(retrievedPrompt?.usageNotes).toBe('Ensure all new fields are saved.');
    expect(retrievedPrompt?.targetModels).toEqual(['gpt-4o', 'test-model-1']);
  });

  it('updatePrompt should update new PromptVariant fields', async () => {
    const promptData = { name: 'Update Test', prompt: 'Initial', variables: {} };
    const addedPrompt = await promptStore.addPrompt(promptData);

    const updates: Partial<PromptVariant> = {
      promptType: 'updated_type',
      tags: ['updated_tag'],
      targetModels: ['new-model']
    };
    await promptStore.updatePrompt(addedPrompt.id, updates);

    const retrievedPrompt = promptStore.getPrompts().find(p => p.id === addedPrompt.id);
    expect(retrievedPrompt?.promptType).toBe('updated_type');
    expect(retrievedPrompt?.tags).toEqual(['updated_tag']);
    expect(retrievedPrompt?.targetModels).toEqual(['new-model']);
  });

  describe('runIntegratedTest', () => {
    it('should include runMetadata in the TestResult', async () => {
      (agentSimulationService.runSimulation as Mock).mockResolvedValue({
        scenario: 'Test scenario',
        agentResponses: [{ agentId: 'agent1', agentName: 'TestAgent', response: 'Mock response' }],
        summary: 'Mock summary',
        metadata: { totalTokens: 10, duration: 100, averageConfidence: 0.9 }
      });

      const prompt = await promptStore.addPrompt({ name: 'p1', prompt: 'test', variables: {} });
      const agent = await promptStore.addAgent({ 
        name: 'ag1', 
        role: 'r', 
        systemPrompt: 's', 
        personality: 'p',
        enabled: true
      });

      const testGoal = "Verify runMetadata population";
      const result = await promptStore.runIntegratedTest([prompt.id], [agent.id], 'Test Scenario', testGoal);

      const storedResult = promptStore.getResults().find(r => r.id === result.id);
      expect(storedResult).toBeDefined();
      expect(storedResult?.runMetadata).toBeDefined();
      expect(storedResult?.runMetadata?.testGoal).toBe(testGoal);
      expect(storedResult?.runMetadata?.inputSample).toBe('Test Scenario');
    });
  });
});
