
export interface ModelBehaviorProfile {
  provider: string;
  model: string;
  characteristics: {
    responseStyle: 'concise' | 'verbose' | 'balanced';
    creativity: number; // 0-100
    factualAccuracy: number; // 0-100
    followsInstructions: number; // 0-100
    contextRetention: number; // 0-100
    reasoningDepth: 'surface' | 'moderate' | 'deep' | 'analytical';
  };
  behaviorTags: string[];
  toneHints: string[];
  formatStrengths: string[];
  recommendations: {
    bestForTasks: string[];
    temperatureRange: [number, number];
    maxTokensGuideline: number;
    systemPromptStyle: 'directive' | 'conversational' | 'structured';
  };
  adaptationHints: {
    promptModifications: string[];
    outputFormatting: string[];
    chainCompatibility: 'excellent' | 'good' | 'fair' | 'limited';
  };
}

export interface ModelConfiguration {
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  behaviorOverrides?: Partial<ModelBehaviorProfile['characteristics']>;
}

class ModelBehaviorService {
  private profiles: Map<string, ModelBehaviorProfile> = new Map();

  constructor() {
    this.initializeDefaultProfiles();
  }

  private initializeDefaultProfiles(): void {
    // OpenAI Models
    this.addProfile({
      provider: 'openai',
      model: 'gpt-4o',
      characteristics: {
        responseStyle: 'balanced',
        creativity: 85,
        factualAccuracy: 95,
        followsInstructions: 90,
        contextRetention: 95,
        reasoningDepth: 'deep'
      },
      behaviorTags: ['analytical', 'creative', 'instruction-following', 'versatile'],
      toneHints: ['professional', 'helpful', 'detailed'],
      formatStrengths: ['structured-output', 'code-generation', 'analysis'],
      recommendations: {
        bestForTasks: ['complex-analysis', 'creative-writing', 'code-generation', 'problem-solving'],
        temperatureRange: [0.3, 0.8],
        maxTokensGuideline: 2048,
        systemPromptStyle: 'structured'
      },
      adaptationHints: {
        promptModifications: ['use-clear-structure', 'provide-examples', 'specify-format'],
        outputFormatting: ['json-friendly', 'markdown-capable', 'code-blocks'],
        chainCompatibility: 'excellent'
      }
    });

    this.addProfile({
      provider: 'openai',
      model: 'o3-mini-2025-01-31',
      characteristics: {
        responseStyle: 'concise',
        creativity: 70,
        factualAccuracy: 92,
        followsInstructions: 95,
        contextRetention: 85,
        reasoningDepth: 'analytical'
      },
      behaviorTags: ['efficient', 'precise', 'reasoning-focused', 'cost-effective'],
      toneHints: ['direct', 'accurate', 'concise'],
      formatStrengths: ['logical-reasoning', 'step-by-step', 'problem-solving'],
      recommendations: {
        bestForTasks: ['logical-reasoning', 'math-problems', 'analysis', 'debugging'],
        temperatureRange: [0.1, 0.5],
        maxTokensGuideline: 1024,
        systemPromptStyle: 'directive'
      },
      adaptationHints: {
        promptModifications: ['be-specific', 'ask-for-reasoning', 'structured-approach'],
        outputFormatting: ['step-by-step', 'numbered-lists', 'clear-conclusions'],
        chainCompatibility: 'excellent'
      }
    });

    // Anthropic Models
    this.addProfile({
      provider: 'anthropic',
      model: 'claude-sonnet-4-20250514',
      characteristics: {
        responseStyle: 'verbose',
        creativity: 90,
        factualAccuracy: 93,
        followsInstructions: 88,
        contextRetention: 98,
        reasoningDepth: 'deep'
      },
      behaviorTags: ['thoughtful', 'nuanced', 'creative', 'ethical', 'comprehensive'],
      toneHints: ['thoughtful', 'nuanced', 'helpful', 'ethical'],
      formatStrengths: ['long-form-content', 'creative-writing', 'detailed-analysis'],
      recommendations: {
        bestForTasks: ['creative-writing', 'detailed-analysis', 'ethical-reasoning', 'research'],
        temperatureRange: [0.4, 0.9],
        maxTokensGuideline: 4096,
        systemPromptStyle: 'conversational'
      },
      adaptationHints: {
        promptModifications: ['encourage-elaboration', 'ask-for-perspectives', 'request-examples'],
        outputFormatting: ['detailed-explanations', 'multiple-perspectives', 'thoughtful-conclusions'],
        chainCompatibility: 'good'
      }
    });

    // Google Models
    this.addProfile({
      provider: 'google',
      model: 'gemini-2.5-flash',
      characteristics: {
        responseStyle: 'balanced',
        creativity: 80,
        factualAccuracy: 88,
        followsInstructions: 85,
        contextRetention: 90,
        reasoningDepth: 'moderate'
      },
      behaviorTags: ['fast', 'multimodal', 'practical', 'efficient'],
      toneHints: ['practical', 'efficient', 'straightforward'],
      formatStrengths: ['multimodal-content', 'quick-responses', 'practical-solutions'],
      recommendations: {
        bestForTasks: ['quick-responses', 'multimodal-tasks', 'practical-solutions', 'rapid-prototyping'],
        temperatureRange: [0.2, 0.7],
        maxTokensGuideline: 1536,
        systemPromptStyle: 'directive'
      },
      adaptationHints: {
        promptModifications: ['be-direct', 'focus-on-practical', 'use-clear-format'],
        outputFormatting: ['bullet-points', 'practical-steps', 'clear-structure'],
        chainCompatibility: 'good'
      }
    });
  }

  addProfile(profile: ModelBehaviorProfile): void {
    const key = `${profile.provider}:${profile.model}`;
    this.profiles.set(key, profile);
  }

  getProfile(provider: string, model: string): ModelBehaviorProfile | null {
    const key = `${provider}:${model}`;
    return this.profiles.get(key) || null;
  }

  getAllProfiles(): ModelBehaviorProfile[] {
    return Array.from(this.profiles.values());
  }

  getOptimalConfiguration(
    taskType: string,
    desiredTone?: string,
    outputFormat?: string
  ): ModelConfiguration[] {
    const recommendations: ModelConfiguration[] = [];

    for (const profile of this.profiles.values()) {
      let score = 0;

      // Task type matching
      if (profile.recommendations.bestForTasks.includes(taskType)) {
        score += 3;
      }

      // Tone matching
      if (desiredTone && profile.toneHints.includes(desiredTone)) {
        score += 2;
      }

      // Format matching
      if (outputFormat && profile.formatStrengths.includes(outputFormat)) {
        score += 2;
      }

      if (score > 0) {
        recommendations.push({
          provider: profile.provider,
          model: profile.model,
          temperature: (profile.recommendations.temperatureRange[0] + profile.recommendations.temperatureRange[1]) / 2,
          maxTokens: profile.recommendations.maxTokensGuideline,
          systemPrompt: this.generateSystemPrompt(profile, taskType, desiredTone)
        });
      }
    }

    // Sort by compatibility and characteristics
    return recommendations.sort((a, b) => {
      const profileA = this.getProfile(a.provider, a.model)!;
      const profileB = this.getProfile(b.provider, b.model)!;
      
      // Prefer models with better instruction following for structured tasks
      return profileB.characteristics.followsInstructions - profileA.characteristics.followsInstructions;
    });
  }

  private generateSystemPrompt(
    profile: ModelBehaviorProfile,
    taskType: string,
    desiredTone?: string
  ): string {
    let systemPrompt = '';

    switch (profile.recommendations.systemPromptStyle) {
      case 'directive':
        systemPrompt = `You are an AI assistant specialized in ${taskType}. `;
        if (desiredTone) {
          systemPrompt += `Maintain a ${desiredTone} tone. `;
        }
        systemPrompt += 'Follow instructions precisely and provide clear, structured responses.';
        break;

      case 'conversational':
        systemPrompt = `You are a helpful AI assistant with expertise in ${taskType}. `;
        if (desiredTone) {
          systemPrompt += `Please use a ${desiredTone} tone throughout our conversation. `;
        }
        systemPrompt += 'Feel free to ask clarifying questions and provide detailed explanations.';
        break;

      case 'structured':
        systemPrompt = `# AI Assistant Configuration\n\n`;
        systemPrompt += `**Specialization**: ${taskType}\n`;
        if (desiredTone) {
          systemPrompt += `**Tone**: ${desiredTone}\n`;
        }
        systemPrompt += `**Instructions**: Provide well-structured, comprehensive responses with clear reasoning.`;
        break;
    }

    return systemPrompt;
  }

  adaptPromptForModel(
    prompt: string,
    provider: string,
    model: string
  ): string {
    const profile = this.getProfile(provider, model);
    if (!profile) return prompt;

    let adaptedPrompt = prompt;

    // Apply model-specific adaptations
    for (const modification of profile.adaptationHints.promptModifications) {
      switch (modification) {
        case 'use-clear-structure':
          if (!adaptedPrompt.includes('\n') && adaptedPrompt.length > 100) {
            adaptedPrompt = this.addStructureToPrompt(adaptedPrompt);
          }
          break;

        case 'provide-examples':
          if (!adaptedPrompt.toLowerCase().includes('example')) {
            adaptedPrompt += '\n\nPlease provide examples in your response.';
          }
          break;

        case 'specify-format':
          if (!adaptedPrompt.toLowerCase().includes('format')) {
            adaptedPrompt += '\n\nPlease structure your response clearly.';
          }
          break;

        case 'be-specific':
          if (adaptedPrompt.length < 50) {
            adaptedPrompt += ' Please be specific and detailed in your response.';
          }
          break;

        case 'ask-for-reasoning':
          if (!adaptedPrompt.toLowerCase().includes('reason') && !adaptedPrompt.toLowerCase().includes('explain')) {
            adaptedPrompt += '\n\nPlease explain your reasoning.';
          }
          break;
      }
    }

    return adaptedPrompt;
  }

  private addStructureToPrompt(prompt: string): string {
    const sentences = prompt.split('. ');
    if (sentences.length < 3) return prompt;

    return `## Task\n${sentences[0]}.\n\n## Requirements\n${sentences.slice(1).join('. ')}`;
  }

  getBehaviorTags(provider: string, model: string): string[] {
    const profile = this.getProfile(provider, model);
    return profile?.behaviorTags || [];
  }

  getToneHints(provider: string, model: string): string[] {
    const profile = this.getProfile(provider, model);
    return profile?.toneHints || [];
  }

  isChainCompatible(provider: string, model: string): boolean {
    const profile = this.getProfile(provider, model);
    return profile?.adaptationHints.chainCompatibility === 'excellent' ||
           profile?.adaptationHints.chainCompatibility === 'good';
  }
}

export const modelBehaviorService = new ModelBehaviorService();
