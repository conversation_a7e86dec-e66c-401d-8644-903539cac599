import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Upload, 
  Database, 
  Trash2, 
  RefreshCw,
  HardDrive,
  Archive,
  FileText,
  AlertCircle,
  CheckCircle,
  Loader2,
  Share2,
  Package,
  ArchiveRestore
} from 'lucide-react';
import { usePromptStore } from '@/hooks/usePromptStore';
import { toast } from '@/hooks/use-toast';
import {
  exportToPromptXArchive,
  downloadBlobUtil,
  syncWithCatalystPlaceholder,
  exportToJsonDump,
  StandaloneExportData,
  PROMPT_STUDIO_APP_NAME,
  PROMPT_STUDIO_APP_VERSION
} from '@/lib/export-tools';
import { importFromPromptXArchive } from '@/lib/import-tools';

export const DataManagement = () => {
  const { 
    createBackup, 
    restoreBackup, 
    getBackups, 
    deleteBackup,
    exportData: storeExportData,
    importData,
    getStats,
    cleanup,
    prompts,
    agents,
    results,
    importPromptXData
  } = usePromptStore();

  const [backups, setBackups] = useState<any[]>([]);
  const [stats, setStats] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [backupName, setBackupName] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [backupsData, statsData] = await Promise.all([
        getBackups(),
        getStats()
      ]);
      setBackups(backupsData);
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const handleCreateBackup = async () => {
    if (!backupName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a backup name",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      await createBackup(backupName);
      setBackupName('');
      await loadData();
      
      toast({
        title: "Backup Created",
        description: `Backup "${backupName}" created successfully`,
      });
    } catch (error) {
      toast({
        title: "Backup Failed",
        description: error instanceof Error ? error.message : "Failed to create backup",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestoreBackup = async (backupId: string) => {
    setIsLoading(true);
    try {
      await restoreBackup(backupId);
      await loadData();
      
      toast({
        title: "Backup Restored",
        description: "Data has been restored from backup",
      });
    } catch (error) {
      toast({
        title: "Restore Failed",
        description: error instanceof Error ? error.message : "Failed to restore backup",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteBackup = async (backupId: string) => {
    setIsLoading(true);
    try {
      await deleteBackup(backupId);
      await loadData();
      
      toast({
        title: "Backup Deleted",
        description: "Backup has been deleted",
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete backup",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = async () => { // This is the existing full JSON export
    setIsLoading(true);
    try {
      const data = await storeExportData(); // Use the aliased storeExportData
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      downloadBlobUtil(blob, `prompt-studio-full-export-${new Date().toISOString().split('T')[0]}.json`);
      
      toast({
        title: "Full JSON Export Complete",
        description: "All data has been exported successfully as a single JSON file.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportPromptXArchive = async () => {
    setIsLoading(true);
    try {
      const exportPayload: StandaloneExportData = {
        prompts: prompts, // from usePromptStore
        agents: agents,   // from usePromptStore
        results: results, // from usePromptStore
        metadata: {
          appName: PROMPT_STUDIO_APP_NAME,
          appVersion: PROMPT_STUDIO_APP_VERSION,
          exportDate: new Date().toISOString(),
          description: "Prompt Studio Data Export (.promptx)"
        }
      };
      const blob = await exportToPromptXArchive(exportPayload);
      downloadBlobUtil(blob, `${PROMPT_STUDIO_APP_NAME.toLowerCase()}_export_${new Date().toISOString().split('T')[0]}.promptx`);
      toast({
        title: "Export to .promptx Complete",
        description: "Data has been exported as a .promptx archive.",
      });
    } catch (error: any) {
      console.error("Export to .promptx failed:", error);
      toast({
        title: "Export to .promptx Failed",
        description: error.message || "An unknown error occurred.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportNewJsonDump = async () => { // New handler for the standalone JSON dump
    setIsLoading(true);
    try {
      const exportPayload: StandaloneExportData = {
        prompts: prompts,
        agents: agents,
        results: results,
        metadata: {
          appName: PROMPT_STUDIO_APP_NAME,
          appVersion: PROMPT_STUDIO_APP_VERSION,
          exportDate: new Date().toISOString(),
          description: "Prompt Studio Data Dump (.json)"
        }
      };
      const blob = exportToJsonDump(exportPayload); // Using the new standalone function
      downloadBlobUtil(blob, `${PROMPT_STUDIO_APP_NAME.toLowerCase()}_dump_${new Date().toISOString().split('T')[0]}.json`);
      toast({
        title: "JSON Dump Export Complete",
        description: "Data has been exported as a JSON dump.",
      });
    } catch (error: any) {
      console.error("JSON Dump Export failed:", error);
      toast({
        title: "JSON Dump Export Failed",
        description: error.message || "An unknown error occurred.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncWithCatalyst = async () => {
    setIsLoading(true);
    toast({ title: "Attempting Catalyst Sync...", description: "Please wait."});
    try {
      const exportPayload: StandaloneExportData = {
        prompts: prompts,
        agents: agents,
        results: [], // Optionally decide if results are synced for Catalyst
        metadata: { exportDate: new Date().toISOString(), appName: PROMPT_STUDIO_APP_NAME, appVersion: PROMPT_STUDIO_APP_VERSION }
      };
      await syncWithCatalystPlaceholder(exportPayload);
      // The placeholder currently throws an error or alerts, so success toast might not be reached.
      // If it were a real API call that could succeed:
      // toast({ title: "Catalyst Sync Successful", description: "Data synced with Catalyst." });
    } catch (error: any) {
      console.error("Catalyst Sync placeholder error:", error);
      // Toast is handled by the placeholder function itself via alert
      // but we can add another one if needed or if alert is removed.
      // toast({
      //   title: "Catalyst Sync Not Implemented",
      //   description: error.message || "This feature is not yet available.",
      //   variant: "default"
      // });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      await importData(data);
      await loadData();
      
      toast({
        title: "Import Complete",
        description: "Data has been imported successfully",
      });
    } catch (error) {
      toast({
        title: "Import Failed",
        description: error instanceof Error ? error.message : "Failed to import data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      // Reset file input
      event.target.value = '';
    }
  };

  const handleImportPromptX = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    toast({ title: "Importing .promptx Archive...", description: "Please wait." });
    try {
      const importedData = await importFromPromptXArchive(file);

      if (importedData.errors && importedData.errors.length > 0) {
        importedData.errors.slice(0,3).forEach(err => {
          toast({ title: `Import Warning: ${err.itemPath}`, description: err.message, variant: "destructive", duration: 5000 });
        });
      }

      const result = await importPromptXData(importedData);
      await loadData();

      toast({
        title: "Import from .promptx Complete",
        description: `${result.successCount} items imported successfully. ${result.errorCount} errors.`,
        variant: result.errorCount > 0 ? "default" : "default"
      });
      
      if (result.errorCount > 0) {
        console.error("Import errors:", result.errors);
      }

    } catch (error) {
      toast({
        title: "Import Failed",
        description: error instanceof Error ? error.message : "Failed to import .promptx archive",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      event.target.value = ''; // Reset file input
    }
  };

  const handleCleanup = async () => {
    setIsLoading(true);
    try {
      await cleanup(30); // Keep last 30 days
      await loadData();
      
      toast({
        title: "Cleanup Complete",
        description: "Old data has been cleaned up",
      });
    } catch (error) {
      toast({
        title: "Cleanup Failed",
        description: error instanceof Error ? error.message : "Failed to cleanup data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Database Statistics */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-400" />
            Database Statistics
          </CardTitle>
          <CardDescription>
            Overview of your stored data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{stats.prompts || 0}</div>
              <div className="text-sm text-slate-400">Prompts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{stats.agents || 0}</div>
              <div className="text-sm text-slate-400">Agents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{stats.results || 0}</div>
              <div className="text-sm text-slate-400">Results</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">{formatBytes(stats.totalSize || 0)}</div>
              <div className="text-sm text-slate-400">Total Size</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="backups" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="backups">Backups</TabsTrigger>
          <TabsTrigger value="export">Export/Import</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        </TabsList>

        <TabsContent value="backups" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5 text-green-400" />
                Create Backup
              </CardTitle>
              <CardDescription>
                Create a backup of your current data
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="backup-name">Backup Name</Label>
                  <Input
                    id="backup-name"
                    value={backupName}
                    onChange={(e) => setBackupName(e.target.value)}
                    placeholder="Enter backup name..."
                    className="bg-slate-900/50 border-slate-600 text-white"
                  />
                </div>
                <div className="flex items-end">
                  <Button 
                    onClick={handleCreateBackup}
                    disabled={isLoading || !backupName.trim()}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Archive className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle>Existing Backups</CardTitle>
              <CardDescription>
                Manage your data backups
              </CardDescription>
            </CardHeader>
            <CardContent>
              {backups.length === 0 ? (
                <div className="text-center py-8 text-slate-400">
                  <Archive className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No backups found</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {backups.map((backup) => (
                    <div key={backup.id} className="flex items-center justify-between p-3 bg-slate-900/30 rounded-lg">
                      <div>
                        <h4 className="font-medium text-slate-200">{backup.name}</h4>
                        <p className="text-sm text-slate-400">
                          {new Date(backup.createdAt).toLocaleString()} • {formatBytes(backup.size)}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleRestoreBackup(backup.id)}
                          disabled={isLoading}
                          size="sm"
                          variant="outline"
                        >
                          <RefreshCw className="w-3 h-3 mr-1" />
                          Restore
                        </Button>
                        <Button
                          onClick={() => handleDeleteBackup(backup.id)}
                          disabled={isLoading}
                          size="sm"
                          variant="destructive"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5 text-blue-400" />
                  Export All (Legacy JSON)
                </CardTitle>
                <CardDescription>
                  Download all store data as a single JSON file (legacy format).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleExportData}
                  disabled={isLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {isLoading ? ( <Loader2 className="w-4 h-4 mr-2 animate-spin" /> ) : ( <Download className="w-4 h-4 mr-2" /> )}
                  Export Legacy .json
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-sky-400" />
                  Export All (JSON Dump)
                </CardTitle>
                <CardDescription>
                  Download prompts, agents, and results as a structured JSON file.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleExportNewJsonDump}
                  disabled={isLoading}
                  className="w-full bg-sky-600 hover:bg-sky-700"
                >
                  {isLoading ? ( <Loader2 className="w-4 h-4 mr-2 animate-spin" /> ) : ( <FileText className="w-4 h-4 mr-2" /> )}
                  Export .json
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-teal-400" />
                  Export to .promptx
                </CardTitle>
                <CardDescription>
                  Export data in the .promptx ZIP format for interoperability.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleExportPromptXArchive}
                  disabled={isLoading}
                  className="w-full bg-teal-600 hover:bg-teal-700"
                >
                  {isLoading ? ( <Loader2 className="w-4 h-4 mr-2 animate-spin" /> ) : ( <Package className="w-4 h-4 mr-2" /> )}
                  Export .promptx
                </Button>
              </CardContent>
            </Card>

            {/* Catalyst Sync Card - Added */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5 text-purple-400" />
                  Catalyst Sync
                </CardTitle>
                <CardDescription>
                  Synchronize assets with Catalyst (Feature Preview).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleSyncWithCatalyst}
                  disabled={true} // Feature is a placeholder, so disabled
                  className="w-full bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
                >
                  {isLoading ? ( <Loader2 className="w-4 h-4 mr-2 animate-spin" /> ) : ( <Share2 className="w-4 h-4 mr-2" /> )}
                  Sync with Catalyst (Coming Soon)
                </Button>
              </CardContent>
            </Card>

            {/* Import Card - original structure */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5 text-green-400" />
                  Import from JSON Dump
                </CardTitle>
                <CardDescription>
                  Upload and restore from a full JSON data dump (legacy or specific export).
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Input
                    type="file"
                    accept=".json"
                    onChange={handleImportData}
                    disabled={isLoading}
                    className="bg-slate-900/50 border-slate-600 text-white file:bg-green-600 file:text-white file:border-0 file:rounded file:px-3 file:py-1"
                  />
                  <Alert className="border-yellow-600/30 bg-yellow-900/20">
                    <AlertCircle className="h-4 w-4 text-yellow-400" />
                    <AlertDescription className="text-yellow-200">
                      Importing will replace all existing data. Create a backup first!
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>

            {/* New .promptx Import Card */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ArchiveRestore className="h-5 w-5 text-indigo-400" />
                  Import from .promptx Archive
                </CardTitle>
                <CardDescription>
                  Import prompts, agents, flows, etc., from a .promptx ZIP archive.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Input
                    type="file"
                    accept=".promptx"
                    onChange={handleImportPromptX}
                    disabled={isLoading}
                    className="bg-slate-900/50 border-slate-600 text-white file:bg-indigo-600 file:text-white file:border-0 file:rounded file:px-3 file:py-1"
                  />
                   <Alert className="border-yellow-600/30 bg-yellow-900/20">
                    <AlertCircle className="h-4 w-4 text-yellow-400" />
                    <AlertDescription className="text-yellow-200">
                      Importing may add new items. It does not typically overwrite existing items with the same name unless IDs match and the import logic handles updates (currently basic add).
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HardDrive className="h-5 w-5 text-purple-400" />
                Database Maintenance
              </CardTitle>
              <CardDescription>
                Clean up old data and optimize storage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-slate-900/30 rounded-lg">
                <div>
                  <h4 className="font-medium text-slate-200">Cleanup Old Data</h4>
                  <p className="text-sm text-slate-400">
                    Remove test results older than 30 days and excess backups
                  </p>
                </div>
                <Button
                  onClick={handleCleanup}
                  disabled={isLoading}
                  variant="outline"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Cleanup
                    </>
                  )}
                </Button>
              </div>

              <Alert className="border-green-600/30 bg-green-900/20">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <AlertDescription className="text-green-200">
                  Your data is automatically backed up locally using IndexedDB for persistence across browser sessions.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
