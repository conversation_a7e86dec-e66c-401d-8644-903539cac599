import { PromptVariant } from '@/store/promptStore';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, GitBranch, PlaySquare, RefreshCw, Camera } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { aiService } from '@/lib/ai-service';
import { interpolateVariables } from '@/lib/utils';
import { useLoading } from '@/lib/performance';
import { toast } from '@/hooks/use-toast';

interface VariantEditorProps {
  variant: PromptVariant | null;
  allVariants: PromptVariant[];
  onUpdateVariant: (id: string, field: keyof PromptVariant, value: any) => void;
}

const tryFormatJson = (jsonString: string | null | undefined) => {
  if (!jsonString) return '';
  try {
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
  } catch (error) {
    return jsonString;
  }
};

export const VariantEditor = ({ variant, allVariants, onUpdateVariant }: VariantEditorProps) => {
  const [localVariant, setLocalVariant] = useState<PromptVariant | null>(null);
  const [variableInput, setVariableInput] = useState({ key: '', value: '' });
  const [newSubVariant, setNewSubVariant] = useState('');

  // State for Live Preview
  const [availablePreviewModels, setAvailablePreviewModels] = useState<Array<{ id: string; name: string; provider: string }>>([]);
  const [selectedPreviewModel, setSelectedPreviewModel] = useState<string>('');
  const [previewVariableValues, setPreviewVariableValues] = useState<Record<string, string>>({});
  const [previewOutput, setPreviewOutput] = useState<string | null>(null);
  const [previewMetadata, setPreviewMetadata] = useState<any>(null);
  const { setLoading, getLoadingState } = useLoading('variant-editor-preview');

  // Get loading state
  const isPreviewLoading = getLoadingState().isLoading;

  useEffect(() => {
    // Use a deep copy to avoid direct mutation of prop
    const newLocalVariant = variant ? JSON.parse(JSON.stringify(variant)) : null;
    setLocalVariant(newLocalVariant);
    if (newLocalVariant) {
      // Initialize previewVariableValues from the variant's variables
      setPreviewVariableValues(newLocalVariant.variables || {});
    }
    setPreviewOutput(null); // Clear preview when variant changes
    setPreviewMetadata(null);
  }, [variant]);

  useEffect(() => {
    // Load available models for preview
    const models = aiService.getAvailableProviders().flatMap(provider =>
      provider.models.map(modelName => ({
        id: `${provider.name.toLowerCase()}:${modelName}`, // Consistent ID like "openai:gpt-4o"
        name: `${provider.name} - ${modelName}`,
        provider: provider.name,
      }))
    );
    setAvailablePreviewModels(models);
    if (models.length > 0 && !selectedPreviewModel) {
      // Try to set a default model, e.g., first OpenAI model if available
      const defaultOpenAI = models.find(m => m.provider === "OpenAI");
      setSelectedPreviewModel(defaultOpenAI ? defaultOpenAI.id : models[0].id);
    }
  }, []); // Run once on mount

  const handlePreviewVariableChange = (key: string, value: string) => {
    setPreviewVariableValues(prev => ({ ...prev, [key]: value }));
  };

  const handleRunPreview = async () => {
    if (!localVariant || !selectedPreviewModel) {
      toast({ title: "Error", description: "Prompt content and a model must be selected for preview.", variant: "destructive"});
      return;
    }
    setLoading(true);
    setPreviewOutput(null);
    setPreviewMetadata(null);

    try {
      const [provider, modelName] = selectedPreviewModel.split(':');
      const processedPrompt = interpolateVariables(localVariant.prompt, previewVariableValues);
      const processedSystemPrompt = localVariant.systemPrompt
        ? interpolateVariables(localVariant.systemPrompt, previewVariableValues)
        : undefined;

      const messages: any[] = [];
      if (processedSystemPrompt) {
        messages.push({ role: 'system', content: processedSystemPrompt });
      }
      messages.push({ role: 'user', content: processedPrompt });

      const startTime = Date.now();
      const response = await aiService.generateResponse({
        provider,
        model: modelName,
        messages,
      });
      const endTime = Date.now();

      setPreviewOutput(response.content);
      setPreviewMetadata({
        tokens: response.usage,
        runtime: endTime - startTime,
        modelUsed: `${provider}:${modelName}`
      });

    } catch (error: any) {
      toast({ title: "Preview Error", description: error.message || "Failed to get preview.", variant: "destructive"});
      setPreviewOutput(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSnapshotPreview = () => {
    if (!localVariant || !previewOutput || previewOutput.startsWith('Error:')) {
      toast({ title: "Snapshot Error", description: "No valid preview output to snapshot.", variant: "destructive" });
      return;
    }

    const snapshot = {
      output: previewOutput,
      metadata: { // Aligning with preferred structure
        model: previewMetadata?.modelUsed || 'N/A',
        tokens: previewMetadata?.tokens?.totalTokens || 0,
        runtime: `${previewMetadata?.runtime || 0}ms`,
      },
      variablesUsed: { ...previewVariableValues },
      timestamp: new Date().toISOString(),
    };

    // Call the prop to update the variant in the store
    onUpdateVariant(localVariant.id, 'lastPreviewSnapshot', snapshot);
    // Also update local state if we want immediate reflection before potential prop-driven re-render
    setLocalVariant(prev => prev ? ({ ...prev, lastPreviewSnapshot: snapshot, updatedAt: new Date() }) : null);

    toast({ title: "Preview Snapshotted", description: "The current preview has been saved with this prompt variant." });
  };

  if (!localVariant) {
    return (
      <div className="lg:col-span-3 flex items-center justify-center h-full">
        <p className="text-slate-400">Select a variant to edit or create a new one.</p>
      </div>
    );
  }

  const handleFieldChange = (field: keyof PromptVariant, value: any) => {
    if (localVariant) {
      // For direct properties of localVariant
      const updatedVariant = { ...localVariant, [field]: value, updatedAt: new Date() };
      setLocalVariant(updatedVariant);
      onUpdateVariant(localVariant.id, field, value); // Propagate specific field update
    }
  };

  const handleNestedFieldChange = (field: keyof PromptVariant, nestedField: string, value: any) => {
     if (localVariant && typeof (localVariant as any)[field] === 'object') {
      const updatedVariant = {
        ...localVariant,
        [field]: {
          ...(localVariant as any)[field],
          [nestedField]: value,
        },
        updatedAt: new Date(),
      };
      setLocalVariant(updatedVariant);
      onUpdateVariant(localVariant.id, field, updatedVariant[field]);
    }
  };

  const handleAddVariable = () => {
    if (!variableInput.key.trim() || !localVariant) return;
    const newVariables = {
      ...(localVariant.variables || {}),
      [variableInput.key.trim()]: variableInput.value
    };
    handleFieldChange('variables', newVariables);
    setVariableInput({ key: '', value: '' });
  };

  const handleRemoveVariable = (keyToRemove: string) => {
    if (!localVariant) return;
    const newVariables = { ...localVariant.variables };
    delete newVariables[keyToRemove];
    handleFieldChange('variables', newVariables);
  };

  const handleAddSubVariantEntry = () => {
    if (!localVariant) return;
    const updatedSubVariants = [...(localVariant.experimentalSubVariants || []), ''];
    handleFieldChange('experimentalSubVariants', updatedSubVariants);
  };

  const handleUpdateSubVariantEntry = (index: number, value: string) => {
    if (!localVariant || !localVariant.experimentalSubVariants) return;
    const updatedSubVariants = localVariant.experimentalSubVariants.map((sv, i) => i === index ? value : sv);
    handleFieldChange('experimentalSubVariants', updatedSubVariants);
  };

  const handleRemoveSubVariantEntry = (index: number) => {
    if (!localVariant || !localVariant.experimentalSubVariants) return;
    const updatedSubVariants = localVariant.experimentalSubVariants.filter((_, i) => i !== index);
    handleFieldChange('experimentalSubVariants', updatedSubVariants);
  };

  const renderPromptWithVariables = (promptText: string, variables: Record<string, string>) => {
    let rendered = promptText;
    Object.entries(variables).forEach(([key, value]) => {
      rendered = rendered.replace(new RegExp(`{${key}}`, 'g'), value ? `[${value}]` : `{${key}}`);
    });
    return rendered;
  };

  const parentName = localVariant.parentId ? (allVariants.find(p => p.id === localVariant.parentId)?.name || localVariant.parentId.substring(0,6)) : null;

  return (
    <Card className="bg-slate-800/50 border-slate-700 p-6">
      <div className="flex items-center gap-3 mb-6">
        <GitBranch className="w-5 h-5 text-blue-400" />
        <h3 className="text-xl font-semibold text-slate-200 truncate" title={localVariant.name}>{localVariant.name}</h3>
        {parentName && (
          <Badge variant="outline" className="border-yellow-500 text-yellow-300 text-xs whitespace-nowrap" title={`Parent ID: ${localVariant.parentId}`}>
            Fork of: {parentName}
          </Badge>
        )}
        <Badge variant="secondary" className="text-xs whitespace-nowrap">v{localVariant.version || 1}</Badge>
        {localVariant.lineageId && (
            <Badge variant="outline" className="text-xs border-purple-500 text-purple-300" title={`Lineage ID: ${localVariant.lineageId}`}>
                L: {localVariant.lineageId.substring(0,6)}...
            </Badge>
        )}
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-900/50">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="documentation">Documentation</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4 mt-4">
          <div>
            <Label className="text-slate-200">Variant Name</Label>
            <Input
              value={localVariant.name}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
            />
          </div>

          <div>
            <Label className="text-slate-200">System Prompt</Label>
            <Textarea
              value={localVariant.systemPrompt || ''}
              onChange={(e) => handleFieldChange('systemPrompt', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Define system behavior..."
            />
          </div>

          <div>
            <Label className="text-slate-200">Main Prompt</Label>
            <Textarea
              value={localVariant.prompt}
              onChange={(e) => handleFieldChange('prompt', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1 min-h-[120px]"
              placeholder="Use {variable_name} for dynamic content..."
            />
          </div>

          <div>
            <Label className="text-slate-200">Variables</Label>
            <div className="space-y-2 mt-1">
              {Object.entries(localVariant.variables || {}).map(([key, value]) => (
                <div key={key} className="flex items-center gap-2">
                  <Input value={key} readOnly className="bg-slate-950 border-slate-700 text-slate-400 w-1/3"/>
                  <Input
                    value={value}
                    onChange={(e) => {
                        const newVars = {...localVariant.variables, [key]: e.target.value};
                        handleFieldChange('variables', newVars);
                    }}
                    className="bg-slate-900/50 border-slate-600 text-white w-2/3"
                  />
                  <Button variant="ghost" size="icon" onClick={() => handleRemoveVariable(key)} className="text-red-400 hover:text-red-300">
                    <Trash2 className="w-4 h-4"/>
                  </Button>
                </div>
              ))}
              <div className="flex items-center gap-2">
                <Input
                    placeholder="New variable name"
                    value={variableInput.key}
                    onChange={(e) => setVariableInput(prev => ({...prev, key: e.target.value}))}
                    className="bg-slate-900/50 border-slate-600 text-white"
                />
                <Input
                    placeholder="Variable value"
                    value={variableInput.value}
                    onChange={(e) => setVariableInput(prev => ({...prev, value: e.target.value}))}
                    className="bg-slate-900/50 border-slate-600 text-white"
                />
                <Button onClick={handleAddVariable} variant="outline" className="border-slate-600 text-slate-300">Add</Button>
              </div>
            </div>
          </div>

          <div>
            <Label className="text-slate-200">Preview</Label>
            <div className="bg-slate-900/50 p-3 rounded border border-slate-600 mt-1 min-h-[60px]">
              <p className="text-sm text-slate-300 whitespace-pre-wrap">
                {renderPromptWithVariables(localVariant.prompt, localVariant.variables || {})}
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="documentation" className="space-y-4 mt-4">
          <div>
            <Label className="text-slate-200">Purpose & Utility</Label>
            <Textarea
              value={localVariant.purpose || ''}
              onChange={(e) => handleFieldChange('purpose', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Explain what this variant does and why it's useful..."
            />
          </div>
          <div>
            <Label className="text-slate-200">When to Use</Label>
            <Textarea
              value={localVariant.whenToUse || ''}
              onChange={(e) => handleFieldChange('whenToUse', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Describe ideal use cases and scenarios..."
            />
          </div>
          <div>
            <Label className="text-slate-200">When NOT to Use</Label>
            <Textarea
              value={localVariant.whenNotToUse || ''}
              onChange={(e) => handleFieldChange('whenNotToUse', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Warn about inappropriate use cases..."
            />
          </div>
          <div>
            <Label className="text-slate-200">Usage Notes (Model Tips, Revision Info)</Label>
            <Textarea
              value={localVariant.usageNotes || ''}
              onChange={(e) => handleFieldChange('usageNotes', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="Additional tips, optimization notes, model-specific advice, or revision history..."
            />
          </div>

          <Separator className="my-4 bg-slate-700" />

          <div>
            <Label className="text-slate-200">Prompt Type</Label>
            <Input
              value={localVariant.promptType || ''}
              onChange={(e) => handleFieldChange('promptType', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="e.g., summarization, classification, generation..."
            />
          </div>

          <div>
            <Label className="text-slate-200">Defined Output Format</Label>
            <Input
              value={localVariant.definedOutputFormat || ''}
              onChange={(e) => handleFieldChange('definedOutputFormat', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="e.g., json, markdown_list, plain_text"
            />
          </div>

          <div>
            <Label className="text-slate-200">Tags (comma-separated)</Label>
            <Input
              value={(localVariant.tags || []).join(', ')}
              onChange={(e) => handleFieldChange('tags', e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="e.g., creative, technical, finance"
            />
          </div>

          <div>
            <Label className="text-slate-200">Target Models (comma-separated)</Label>
            <Input
              value={(localVariant.targetModels || []).join(', ')}
              onChange={(e) => handleFieldChange('targetModels', e.target.value.split(',').map(model => model.trim()).filter(model => model))}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="e.g., gpt-4o, claude-3-opus"
            />
          </div>

          <div>
            <Label className="text-slate-200">Desired Tone</Label>
            <Input
              value={localVariant.desiredTone || ''}
              onChange={(e) => handleFieldChange('desiredTone', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="e.g., formal, empathetic, concise"
            />
          </div>

          <div>
            <Label className="text-slate-200">Contextual Depth</Label>
            <Input
              value={localVariant.contextualDepth || ''}
              onChange={(e) => handleFieldChange('contextualDepth', e.target.value)}
              className="bg-slate-900/50 border-slate-600 text-white mt-1"
              placeholder="e.g., shallow, medium, deep (or description)"
            />
          </div>

        </TabsContent>

        <TabsContent value="advanced" className="space-y-4 mt-4">
          <div>
            <Label className="text-slate-200">Experimental Sub-Variants</Label>
            <div className="space-y-2 mt-2">
              {(localVariant.experimentalSubVariants || []).map((subVariant, index) => (
                <div key={index} className="flex gap-2">
                  <Textarea
                    value={subVariant}
                    onChange={(e) => handleUpdateSubVariantEntry(index, e.target.value)}
                    className="bg-slate-900/50 border-slate-600 text-white flex-1"
                    placeholder="Alternative version of this variant..."
                  />
                  <Button variant="ghost" size="sm" onClick={() => handleRemoveSubVariantEntry(index)} className="text-red-400 hover:text-red-300">
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              <Button variant="outline" size="sm" onClick={handleAddSubVariantEntry} className="border-slate-600 text-slate-300">
                <Plus className="w-4 h-4 mr-2" />
                Add Sub-Variant
              </Button>
            </div>
          </div>
          <Separator className="bg-slate-600" />
          <div>
            <Label className="text-slate-200">Variant Build Kit</Label>
            <div className="space-y-3 mt-2">
              <div>
                <Label className="text-slate-400 text-sm">Prompt Frame</Label>
                <Input
                  value={localVariant.buildKit?.frame || ''}
                  onChange={(e) => handleNestedFieldChange('buildKit', 'frame', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  placeholder="[ Component 1 + Component 2 + Component 3 ]"
                />
              </div>
              <div>
                <Label className="text-slate-400 text-sm">Example Structure</Label>
                <Input
                  value={localVariant.buildKit?.example || ''}
                  onChange={(e) => handleNestedFieldChange('buildKit', 'example', e.target.value)}
                  className="bg-slate-900/50 border-slate-600 text-white mt-1"
                  placeholder="Action | target | constraint"
                />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Live Preview Section */}
      <Separator className="my-6 bg-slate-700" />
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <PlaySquare className="w-5 h-5 text-green-400" />
          <h4 className="text-lg font-semibold text-slate-200">Live Preview</h4>
        </div>

        {Object.keys(localVariant.variables || {}).length > 0 && (
          <Card className="bg-slate-900/30 p-4 border-slate-700">
            <Label className="text-sm text-slate-300 mb-2 block">Preview Variables:</Label>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(localVariant.variables || {}).map(([key, defaultValue]) => (
                <div key={key}>
                  <Label htmlFor={`preview-var-${key}`} className="text-xs text-slate-400">{key}</Label>
                  <Input
                    id={`preview-var-${key}`}
                    value={previewVariableValues[key] || ''}
                    onChange={(e) => handlePreviewVariableChange(key, e.target.value)}
                    placeholder={String(defaultValue) || 'Enter value'}
                    className="mt-1 bg-slate-700 border-slate-600 text-white text-sm"
                  />
                </div>
              ))}
            </div>
          </Card>
        )}

        <div className="flex items-end gap-3">
          <div className="flex-grow">
            <Label htmlFor="preview-model" className="text-sm text-slate-300">Model for Preview</Label>
            <Select value={selectedPreviewModel} onValueChange={setSelectedPreviewModel}>
              <SelectTrigger className="mt-1 bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {availablePreviewModels.map(model => (
                  <SelectItem key={model.id} value={model.id}>
                    {model.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button onClick={handleRunPreview} disabled={isPreviewLoading || !selectedPreviewModel} className="bg-green-600 hover:bg-green-700 text-white">
            {isPreviewLoading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <PlaySquare className="w-4 h-4" />}
            <span className="ml-2">Run Preview</span>
          </Button>
        </div>

        {previewOutput !== null && (
          <Card className="bg-slate-900/50 p-4 border-slate-600">
            <Label className="text-sm text-slate-300 mb-1 block">Preview Output:</Label>
            {previewMetadata && (
              <div className="text-xs text-slate-500 mb-2">
                Model: {previewMetadata.modelUsed} | Runtime: {previewMetadata.runtime}ms | Tokens:
                P: {previewMetadata.tokens?.promptTokens || 0},
                C: {previewMetadata.tokens?.completionTokens || 0},
                T: {previewMetadata.tokens?.totalTokens || 0}
              </div>
            )}
            <Textarea
              value={
                (localVariant?.definedOutputFormat === 'json' || localVariant?.expectedOutputFormat === 'json') // Check both, though definedOutputFormat is primary for PromptVariant
                ? tryFormatJson(previewOutput) // Assume tryFormatJson is available or add it
                : previewOutput || ''
              }
              readOnly
              className="bg-slate-800 border-slate-700 text-slate-200 font-mono text-xs min-h-[150px] max-h-[300px]"
              rows={8}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleSnapshotPreview}
              className="mt-2 border-sky-500 text-sky-300 hover:bg-sky-500/20 hover:text-sky-200"
              disabled={!previewOutput || previewOutput.startsWith('Error:')}
            >
              <Camera className="w-4 h-4 mr-2" />
              Snapshot this Preview
            </Button>
          </Card>
        )}
      </div>
    </Card>
  );
};
