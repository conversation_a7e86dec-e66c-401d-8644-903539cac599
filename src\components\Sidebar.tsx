
import { Button } from '@/components/ui/button';
import {
  TestTube,
  BarChart3,
  History,
  Settings as SettingsIcon,
  Users,
  GitBranch,
  Code,
  Link,
  Archive // Using Archive icon for Vault
} from 'lucide-react';

interface SidebarProps {
  activeView: string;
  onViewChange: (view: string) => void;
}

export const Sidebar = ({ activeView, onViewChange }: SidebarProps) => {
  const menuItems = [
    { id: 'tester', label: 'Prompt Tester', icon: TestTube },
    { id: 'variations', label: 'Packs & Variations', icon: GitBranch },
    { id: 'agents', label: 'Agent Studio', icon: Users },
    { id: 'projects', label: 'Project Simulator', icon: Code },
    { id: 'chain-linker', label: 'Chain Linker Canvas', icon: Link },
    { id: 'results', label: 'Results Dashboard', icon: BarChart3 },
    { id: 'history', label: 'Prompt History', icon: History },
    { id: 'vault', label: 'Community Vault', icon: Archive }, // New Vault Link
    { id: 'settings', label: 'Settings', icon: SettingsIcon },
  ];

  return (
    <div className="w-64 bg-slate-800/50 backdrop-blur-sm border-r border-slate-700 h-screen p-4 font-sans">
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <img src="/prompt_studio_icon.png" alt="Prompt Studio" className="h-6 w-6" />
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-sans">
            Prompt Studio
          </h1>
        </div>
      </div>

      <nav className="space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          return (
            <Button
              key={item.id}
              variant={activeView === item.id ? "default" : "ghost"}
              className={`w-full justify-start gap-3 font-sans ${
                activeView === item.id 
                  ? "bg-gradient-to-r from-brand-blue to-brand-purple hover:from-brand-blue/90 hover:to-brand-purple/90 text-white"
                  : "hover:bg-slate-700 text-slate-300"
              }`}
              onClick={() => onViewChange(item.id)}
            >
              <Icon className="h-4 w-4" />
              {item.label}
            </Button>
          );
        })}
      </nav>
    </div>
  );
};
