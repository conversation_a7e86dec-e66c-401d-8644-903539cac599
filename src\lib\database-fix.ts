
import { openDB, DBSchema, IDBPDatabase } from 'idb';

interface PromptStudioDB extends DBSchema {
  prompts: {
    key: string;
    value: {
      id: string;
      content: string;
      metadata: any;
      createdAt: Date;
      updatedAt: Date;
    };
  };
  results: {
    key: string;
    value: {
      id: string;
      data: any;
      createdAt: Date;
    };
  };
  agents: {
    key: string;
    value: {
      id: string;
      name: string;
      config: any;
      createdAt: Date;
    };
  };
  chains: {
    key: string;
    value: {
      id: string;
      name: string;
      nodes: any[];
      connections: any[];
      metadata: any;
      createdAt: Date;
    };
  };
  promptPacks: {
    key: string;
    value: {
      id: string;
      title: string;
      description: string;
      variants: any[];
      metadata: any;
      tags: string[];
      createdAt: Date;
    };
  };
}

class DatabaseService {
  private db: IDBPDatabase<PromptStudioDB> | null = null;
  private initPromise: Promise<void> | null = null;

  async initialize(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.doInitialize();
    return this.initPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      this.db = await openDB<PromptStudioDB>('PromptStudioDB', 2, {
        upgrade(db, oldVersion) {
          console.log('🔄 Upgrading database from version', oldVersion);
          
          // Create object stores
          if (!db.objectStoreNames.contains('prompts')) {
            db.createObjectStore('prompts', { keyPath: 'id' });
          }
          if (!db.objectStoreNames.contains('results')) {
            db.createObjectStore('results', { keyPath: 'id' });
          }
          if (!db.objectStoreNames.contains('agents')) {
            db.createObjectStore('agents', { keyPath: 'id' });
          }
          if (!db.objectStoreNames.contains('chains')) {
            db.createObjectStore('chains', { keyPath: 'id' });
          }
          if (!db.objectStoreNames.contains('promptPacks')) {
            db.createObjectStore('promptPacks', { keyPath: 'id' });
          }
        },
        blocked() {
          console.warn('⚠️ Database upgrade blocked');
        },
        blocking() {
          console.warn('⚠️ Database blocking');
        },
        terminated() {
          console.error('❌ Database connection terminated');
        }
      });
      
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      // Fallback to localStorage for critical data
      this.db = null;
    }
  }

  async save<T extends keyof PromptStudioDB>(store: T, data: PromptStudioDB[T]['value']): Promise<void> {
    await this.initialize();

    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${store}_${data.id}`;
      localStorage.setItem(storageKey, JSON.stringify(data));
      return;
    }

    try {
      await this.db.put(store, data);
    } catch (error) {
      console.error(`Failed to save to ${store}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${store}_${data.id}`;
      localStorage.setItem(storageKey, JSON.stringify(data));
    }
  }

  async get(store: keyof PromptStudioDB, id: string): Promise<any | null> {
    await this.initialize();
    
    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${store}_${id}`;
      const data = localStorage.getItem(storageKey);
      return data ? JSON.parse(data) : null;
    }

    try {
      return await this.db.get(store, id) || null;
    } catch (error) {
      console.error(`Failed to get from ${store}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${store}_${id}`;
      const data = localStorage.getItem(storageKey);
      return data ? JSON.parse(data) : null;
    }
  }

  async getAll(store: keyof PromptStudioDB): Promise<any[]> {
    await this.initialize();
    
    if (!this.db) {
      // Fallback to localStorage
      const items: any[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${store}_`)) {
          const data = localStorage.getItem(key);
          if (data) {
            items.push(JSON.parse(data));
          }
        }
      }
      return items;
    }

    try {
      return await this.db.getAll(store);
    } catch (error) {
      console.error(`Failed to get all from ${store}:`, error);
      // Fallback to localStorage
      const items: any[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(`promptstudio_${store}_`)) {
          const data = localStorage.getItem(key);
          if (data) {
            items.push(JSON.parse(data));
          }
        }
      }
      return items;
    }
  }

  async delete(store: keyof PromptStudioDB, id: string): Promise<void> {
    await this.initialize();
    
    if (!this.db) {
      // Fallback to localStorage
      const storageKey = `promptstudio_${store}_${id}`;
      localStorage.removeItem(storageKey);
      return;
    }

    try {
      await this.db.delete(store, id);
    } catch (error) {
      console.error(`Failed to delete from ${store}:`, error);
      // Fallback to localStorage
      const storageKey = `promptstudio_${store}_${id}`;
      localStorage.removeItem(storageKey);
    }
  }
}

export const databaseService = new DatabaseService();
