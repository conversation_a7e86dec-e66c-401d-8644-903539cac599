
import { PromptVariant } from '@/store/promptStore';
import { databaseService } from './database-fix';
import { errorHandler } from './error-handler';

export interface CatalystPromptPack {
  id: string;
  title: string;
  description: string;
  version: string;
  author: {
    name: string;
    contact?: string;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
    category: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    estimatedTokens: number;
    compatibleModels: string[];
    behaviorTags: string[];
    toneHints: string[];
    formatMetadata: {
      inputFormat: string;
      outputFormat: string;
      structureType: 'freeform' | 'structured' | 'templated';
    };
  };
  systemPrompt?: string;
  variants: CatalystPromptVariant[];
  chains?: CatalystChain[];
  testScenarios: CatalystTestScenario[];
  exportFormat: 'promptx' | 'json' | 'yaml';
}

export interface CatalystPromptVariant {
  id: string;
  name: string;
  prompt: string;
  systemPrompt?: string;
  variables: Record<string, string>;
  metadata: {
    desiredTone: string;
    contextualDepth: 'minimal' | 'moderate' | 'detailed' | 'comprehensive';
    promptType: 'instruction' | 'conversation' | 'completion' | 'analysis';
    definedOutputFormat: string;
    tags: string[];
    behaviorTags: string[];
    modelHints: Record<string, any>;
  };
  performance: {
    averageTokens: number;
    responseTime: number;
    successRate: number;
  };
}

export interface CatalystChain {
  id: string;
  name: string;
  description: string;
  nodes: Array<{
    id: string;
    type: 'prompt' | 'condition' | 'transform' | 'output';
    config: Record<string, any>;
  }>;
  connections: Array<{
    from: string;
    to: string;
    condition?: string;
  }>;
}

export interface CatalystTestScenario {
  id: string;
  name: string;
  description: string;
  inputData: Record<string, any>;
  expectedOutput: {
    format: string;
    criteria: string[];
    quality: number;
  };
  tags: string[];
}

class CatalystIntegrationService {
  async createPromptPack(data: Partial<CatalystPromptPack>): Promise<CatalystPromptPack> {
    const promptPack: CatalystPromptPack = {
      id: data.id || `pack_${Date.now()}`,
      title: data.title || 'Untitled Prompt Pack',
      description: data.description || '',
      version: data.version || '1.0.0',
      author: data.author || { name: 'Anonymous' },
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: data.metadata?.tags || [],
        category: data.metadata?.category || 'general',
        difficulty: data.metadata?.difficulty || 'beginner',
        estimatedTokens: data.metadata?.estimatedTokens || 1000,
        compatibleModels: data.metadata?.compatibleModels || ['gpt-4', 'claude-3-sonnet'],
        behaviorTags: data.metadata?.behaviorTags || [],
        toneHints: data.metadata?.toneHints || [],
        formatMetadata: {
          inputFormat: 'text',
          outputFormat: 'text',
          structureType: 'freeform',
          ...data.metadata?.formatMetadata
        }
      },
      systemPrompt: data.systemPrompt,
      variants: data.variants || [],
      chains: data.chains || [],
      testScenarios: data.testScenarios || [],
      exportFormat: data.exportFormat || 'json'
    };

    try {
      await databaseService.save('promptPacks', promptPack);
      console.log('✅ Catalyst prompt pack created:', promptPack.id);
      return promptPack;
    } catch (error) {
      errorHandler.handleDatabaseError(error as Error, 'create_prompt_pack');
      throw error;
    }
  }

  async updatePromptPack(id: string, updates: Partial<CatalystPromptPack>): Promise<CatalystPromptPack> {
    try {
      const existing = await databaseService.get('promptPacks', id);
      if (!existing) {
        throw new Error(`Prompt pack ${id} not found`);
      }

      const updated: CatalystPromptPack = {
        ...existing,
        ...updates,
        metadata: {
          ...existing.metadata,
          ...updates.metadata,
          updatedAt: new Date()
        }
      };

      await databaseService.save('promptPacks', updated);
      console.log('✅ Catalyst prompt pack updated:', id);
      return updated;
    } catch (error) {
      errorHandler.handleDatabaseError(error as Error, 'update_prompt_pack');
      throw error;
    }
  }

  async getAllPromptPacks(): Promise<CatalystPromptPack[]> {
    try {
      return await databaseService.getAll('promptPacks');
    } catch (error) {
      errorHandler.handleDatabaseError(error as Error, 'get_all_prompt_packs');
      return [];
    }
  }

  async getPromptPack(id: string): Promise<CatalystPromptPack | null> {
    try {
      return await databaseService.get('promptPacks', id);
    } catch (error) {
      errorHandler.handleDatabaseError(error as Error, 'get_prompt_pack');
      return null;
    }
  }

  async deletePromptPack(id: string): Promise<void> {
    try {
      await databaseService.delete('promptPacks', id);
      console.log('✅ Catalyst prompt pack deleted:', id);
    } catch (error) {
      errorHandler.handleDatabaseError(error as Error, 'delete_prompt_pack');
      throw error;
    }
  }

  // Convert legacy PromptVariant to Catalyst format
  convertToCatalyst(variants: PromptVariant[]): CatalystPromptVariant[] {
    return variants.map(variant => ({
      id: variant.id || `variant_${Date.now()}`,
      name: variant.name,
      prompt: variant.prompt,
      systemPrompt: variant.systemPrompt,
      variables: variant.variables,
      metadata: {
        desiredTone: variant.desiredTone || 'neutral',
        contextualDepth: (variant.contextualDepth as any) || 'moderate',
        promptType: (variant.promptType as any) || 'instruction',
        definedOutputFormat: variant.definedOutputFormat || 'text',
        tags: variant.tags || [],
        behaviorTags: [],
        modelHints: {}
      },
      performance: {
        averageTokens: 0,
        responseTime: 0,
        successRate: 0
      }
    }));
  }

  // Export to .promptx format
  async exportToPromptX(packId: string): Promise<string> {
    const pack = await this.getPromptPack(packId);
    if (!pack) {
      throw new Error(`Prompt pack ${packId} not found`);
    }

    const promptxData = {
      format: 'promptx',
      version: '1.0',
      pack: {
        id: pack.id,
        title: pack.title,
        description: pack.description,
        metadata: pack.metadata,
        systemPrompt: pack.systemPrompt,
        variants: pack.variants,
        chains: pack.chains,
        testScenarios: pack.testScenarios
      }
    };

    return JSON.stringify(promptxData, null, 2);
  }

  // Import from .promptx format
  async importFromPromptX(promptxContent: string): Promise<CatalystPromptPack> {
    try {
      const data = JSON.parse(promptxContent);
      
      if (data.format !== 'promptx') {
        throw new Error('Invalid .promptx format');
      }

      const pack = data.pack;
      pack.id = `pack_${Date.now()}`;
      pack.metadata.createdAt = new Date();
      pack.metadata.updatedAt = new Date();

      return await this.createPromptPack(pack);
    } catch (error) {
      errorHandler.handleError(
        error as Error,
        { action: 'import_promptx', component: 'catalyst-integration' }
      );
      throw error;
    }
  }

  // Validate prompt pack against Catalyst standards
  validatePromptPack(pack: CatalystPromptPack): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    if (!pack.title || pack.title.trim().length === 0) {
      errors.push('Title is required');
    }

    if (!pack.description || pack.description.trim().length === 0) {
      errors.push('Description is required');
    }

    if (!pack.variants || pack.variants.length === 0) {
      errors.push('At least one variant is required');
    }

    // Metadata validation
    if (!pack.metadata.tags || pack.metadata.tags.length === 0) {
      errors.push('At least one tag is required');
    }

    if (!pack.metadata.category) {
      errors.push('Category is required');
    }

    // Variant validation
    pack.variants.forEach((variant, index) => {
      if (!variant.name || variant.name.trim().length === 0) {
        errors.push(`Variant ${index + 1}: Name is required`);
      }

      if (!variant.prompt || variant.prompt.trim().length === 0) {
        errors.push(`Variant ${index + 1}: Prompt content is required`);
      }

      if (!variant.metadata.promptType) {
        errors.push(`Variant ${index + 1}: Prompt type is required`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export const catalystIntegrationService = new CatalystIntegrationService();
